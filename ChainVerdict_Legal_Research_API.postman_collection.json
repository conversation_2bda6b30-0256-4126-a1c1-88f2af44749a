{"info": {"_postman_id": "legal-research-api-tests", "name": "ChainVerdict Legal Research API - Premium Features", "description": "Test collection for the enhanced Legal Research API with multi-engine search and premium features", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "1. API Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:3000/api/get", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "get"]}, "description": "Check if the API is running and get information about available endpoints"}, "response": []}, {"name": "2. Legal Research - Basic Query", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"query\": \"What is Section 420 of BNS about fraud?\"\n}"}, "url": {"raw": "http://localhost:3000/api/legal-research", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "legal-research"]}, "description": "Test basic legal research query about BNS Section 420"}, "response": []}, {"name": "3. Legal Research - IPC vs BNS Comparison", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"query\": \"Difference between IPC 420 and BNS 420 fraud provisions with case law examples\"\n}"}, "url": {"raw": "http://localhost:3000/api/legal-research", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "legal-research"]}, "description": "Test complex comparison query between old and new legal codes"}, "response": []}, {"name": "4. Legal Research - Cybercrime Query", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"query\": \"Recent Supreme Court judgments on cybercrime under BNS and IT Act\"\n}"}, "url": {"raw": "http://localhost:3000/api/legal-research", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "legal-research"]}, "description": "Test case law research query for recent cybercrime judgments"}, "response": []}, {"name": "5. Legal Research - Constitutional Law", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"query\": \"Article 21 right to life and personal liberty landmark cases\"\n}"}, "url": {"raw": "http://localhost:3000/api/legal-research", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "legal-research"]}, "description": "Test constitutional law research with landmark cases"}, "response": []}, {"name": "6. Legal Research - Corporate Law", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"query\": \"Companies Act 2013 Section 447 punishment for fraud recent amendments\"\n}"}, "url": {"raw": "http://localhost:3000/api/legal-research", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "legal-research"]}, "description": "Test corporate law research query"}, "response": []}, {"name": "7. Legal Research - Follow-up Query", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"conversation_id\": \"test-conversation-123\",\n    \"query\": \"What are the key differences in punishment between old and new fraud laws?\",\n    \"answer\": \"I need information about corporate fraud cases\",\n    \"question_number\": 1\n}"}, "url": {"raw": "http://localhost:3000/api/legal-research/follow-up", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "legal-research", "follow-up"]}, "description": "Test follow-up query functionality"}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set timestamp for requests", "pm.globals.set('timestamp', new Date().toISOString());"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Basic response validation", "pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has JSON body', function () {", "    pm.response.to.be.json;", "});", "", "// Check for premium features in legal research responses", "if (pm.request.url.path.includes('legal-research')) {", "    pm.test('Premium features present', function () {", "        const jsonData = pm.response.json();", "        if (jsonData.status === 'success') {", "            pm.expect(jsonData).to.have.property('premium_analytics');", "            pm.expect(jsonData.premium_analytics).to.have.property('search_engines_used');", "            pm.expect(jsonData.premium_analytics).to.have.property('total_results_found');", "        }", "    });", "}"]}}], "variable": [{"key": "base_url", "value": "http://localhost:3000", "type": "string"}]}