#!/usr/bin/env python3
"""
Test script for streaming functionality across all 4 AI legal tools.
This script tests the real-time streaming responses to ensure they work correctly.
"""

import requests
import json
import time
import sys

# Test configuration
BASE_URL = "http://localhost:5000"
TEST_TIMEOUT = 30  # seconds

def test_streaming_endpoint(endpoint, data, description):
    """Test a streaming endpoint and display results."""
    print(f"\n🔥 TESTING: {description}")
    print("=" * 60)
    
    url = f"{BASE_URL}{endpoint}"
    
    try:
        # Make streaming request
        response = requests.post(
            url, 
            json=data, 
            stream=True,
            timeout=TEST_TIMEOUT,
            headers={'Accept': 'text/plain'}
        )
        
        if response.status_code == 200:
            print("✅ Connection established, streaming response:")
            print("-" * 40)
            
            chunk_count = 0
            for line in response.iter_lines(decode_unicode=True):
                if line.startswith('data: '):
                    chunk_count += 1
                    try:
                        data_content = line[6:]  # Remove 'data: ' prefix
                        chunk_data = json.loads(data_content)
                        
                        # Display different types of chunks
                        if chunk_data.get('type') == 'metadata':
                            print(f"📊 METADATA: {chunk_data.get('status', 'unknown')}")
                        elif chunk_data.get('type') == 'content':
                            print(f"📝 CONTENT: {chunk_data.get('data', '')[:50]}...")
                        elif chunk_data.get('type') == 'content_chunk':
                            print(f"📝 CHUNK: {chunk_data.get('data', '')[:50]}...")
                        elif chunk_data.get('type') == 'complete':
                            print("✅ COMPLETE: Analysis finished")
                        elif chunk_data.get('type') == 'error':
                            print(f"❌ ERROR: {chunk_data.get('data', 'Unknown error')}")
                        elif chunk_data.get('status') == 'complete':
                            print("🎯 FINAL: Streaming completed")
                            break
                        else:
                            print(f"📦 OTHER: {chunk_data.get('type', 'unknown')} - {str(chunk_data)[:50]}...")
                            
                    except json.JSONDecodeError:
                        print(f"⚠️ Non-JSON chunk: {line[:50]}...")
            
            print(f"\n✅ Streaming test completed! Received {chunk_count} chunks")
            return True
            
        else:
            print(f"❌ HTTP Error {response.status_code}: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print(f"⏰ Request timed out after {TEST_TIMEOUT} seconds")
        return False
    except requests.exceptions.ConnectionError:
        print("🔌 Connection error - is the server running?")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        return False

def main():
    """Run streaming tests for all AI legal tools."""
    print("🚀 CHAINVERDICT STREAMING TESTS")
    print("=" * 60)
    print("Testing real-time streaming responses across all 4 AI legal tools")
    print(f"Server: {BASE_URL}")
    print(f"Timeout: {TEST_TIMEOUT} seconds")
    
    # Test data for each endpoint
    test_cases = [
        {
            "endpoint": "/api/bns-advisor/stream",
            "data": {
                "case_description": "A person was caught stealing a mobile phone from a shop. What are the applicable sections under BNS?",
                "verify": True
            },
            "description": "BNS Advisor Streaming - Theft Case Analysis"
        },
        {
            "endpoint": "/api/legal-advisor/stream", 
            "data": {
                "case_description": "A landlord is trying to evict a tenant without proper notice. What are the tenant's rights?",
                "evidence": ["Rental agreement", "Notice received"],
                "verify": True,
                "include_debate": False
            },
            "description": "Legal Advisor Streaming - Tenant Rights Case"
        },
        {
            "endpoint": "/api/judgement-analyser/stream",
            "data": {
                "judgement_details": "The court found the accused guilty under BNS Section 103 for murder and sentenced to life imprisonment.",
                "crime_details": "Murder of a shopkeeper during robbery",
                "evidence_details": "CCTV footage, witness testimony, forensic evidence",
                "verify": True,
                "include_detailed_analysis": True
            },
            "description": "Judgement Analyser Streaming - Murder Case Analysis"
        },
        {
            "endpoint": "/api/legal-research/stream",
            "data": {
                "query": "What are the key differences between BNS and IPC regarding cybercrime provisions?",
                "conversation_id": None
            },
            "description": "Legal Research Streaming - BNS vs IPC Comparison"
        }
    ]
    
    # Run tests
    results = []
    for test_case in test_cases:
        success = test_streaming_endpoint(
            test_case["endpoint"],
            test_case["data"], 
            test_case["description"]
        )
        results.append({
            "endpoint": test_case["endpoint"],
            "description": test_case["description"],
            "success": success
        })
        
        # Small delay between tests
        time.sleep(2)
    
    # Summary
    print("\n" + "=" * 60)
    print("🎯 STREAMING TEST RESULTS SUMMARY")
    print("=" * 60)
    
    successful_tests = 0
    for result in results:
        status = "✅ PASSED" if result["success"] else "❌ FAILED"
        print(f"{status} - {result['description']}")
        if result["success"]:
            successful_tests += 1
    
    print(f"\n📊 Overall Results: {successful_tests}/{len(results)} tests passed")
    
    if successful_tests == len(results):
        print("🎉 ALL STREAMING TESTS PASSED! Real-time responses are working correctly.")
    elif successful_tests > 0:
        print("⚠️ PARTIAL SUCCESS: Some streaming endpoints are working.")
    else:
        print("❌ ALL TESTS FAILED: Check server status and configuration.")
    
    return successful_tests == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
