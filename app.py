"""
BNSAdvisor API: Unified Legal AI API

This API combines four legal AI frameworks:
1. BNSAdvisor - One-Stop Legal Solutions using BNS (Bharatiya Nyaya Sanhita)
2. CaseAnalysis - Legal Case Analysis Framework with BNS/BNSS/BSA compliance
3. JudgementAnalyser - Legal Judgement Analysis Framework using NEW LEGAL CODES
4. LegalResearch - One stop legal research framework for lawyers

IMPORTANT: All frameworks now use the NEW LEGAL CODES (July 2024):
- BNS (Bharatiya Nyaya Sanhita) - Replaced IPC
- BNSS (Bharatiya Nagarik Suraksha Sanhita) - Replaced CrPC
- BSA (Bharatiya Sakshya Adhiniyam) - Replaced Evidence Act

The API provides endpoints for each framework with comprehensive legal analysis using the updated Indian criminal justice system.
"""

import os
import logging
import json
import time
from typing import Dict, List, Any, Optional, Union, Generator
from flask import Flask, request, jsonify, Response, stream_template
from flask_cors import CORS
import uuid
import datetime

# Import the frameworks from their respective files
from bnsAdvisor import BNSAdvisor
from caseAnalysis import CaseAnalysisAgent
from judgementAnalyser import JudgementAnalyser
from legalResearch import LegalResearch

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("BNSAdvisorAPI")

# ======================================================
# Flask API Implementation
# ======================================================

app = Flask(__name__)
# Enable CORS for all routes and origins
CORS(app, resources={r"/api/*": {"origins": "*"}})

# Initialize the components
bns_advisor_instance = BNSAdvisor()
case_analysis = CaseAnalysisAgent()
judgement_analyser = JudgementAnalyser()
legal_research = LegalResearch()

# 🔥 STREAMING UTILITIES
def create_streaming_response(generator_func, *args, **kwargs) -> Response:
    """
    Create a streaming response from a generator function.

    Args:
        generator_func: Function that returns a generator
        *args, **kwargs: Arguments to pass to the generator function

    Returns:
        Flask Response object with streaming content
    """
    def generate():
        try:
            for chunk in generator_func(*args, **kwargs):
                if chunk:
                    # Format as Server-Sent Events (SSE)
                    if isinstance(chunk, dict):
                        data = json.dumps(chunk)
                    else:
                        data = str(chunk)
                    yield f"data: {data}\n\n"
                    time.sleep(0.01)  # Small delay for better streaming experience
        except Exception as e:
            error_data = json.dumps({"error": str(e), "status": "error"})
            yield f"data: {error_data}\n\n"
        finally:
            # Send completion signal
            yield f"data: {json.dumps({'status': 'complete'})}\n\n"

    return Response(
        generate(),
        mimetype='text/plain',
        headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Content-Type',
        }
    )

def chunk_text(text: str, chunk_size: int = 50) -> Generator[str, None, None]:
    """
    Split text into chunks for streaming.

    Args:
        text: Text to chunk
        chunk_size: Number of words per chunk

    Yields:
        Text chunks
    """
    if not text:
        return

    words = text.split()
    for i in range(0, len(words), chunk_size):
        chunk = ' '.join(words[i:i + chunk_size])
        yield chunk

def stream_analysis_response(analysis_content: str, metadata: dict = None) -> Generator[dict, None, None]:
    """
    Stream analysis response in chunks with metadata.

    Args:
        analysis_content: The analysis text to stream
        metadata: Optional metadata to include

    Yields:
        Dictionary chunks with analysis data
    """
    # Send initial metadata
    if metadata:
        yield {
            "type": "metadata",
            "data": metadata,
            "status": "streaming"
        }

    # Stream the analysis content in chunks
    chunk_count = 0
    for chunk in chunk_text(analysis_content, chunk_size=30):
        chunk_count += 1
        yield {
            "type": "content",
            "data": chunk,
            "chunk_number": chunk_count,
            "status": "streaming"
        }
        time.sleep(0.05)  # Small delay for better user experience

    # Send completion signal
    yield {
        "type": "complete",
        "data": "Analysis complete",
        "total_chunks": chunk_count,
        "status": "complete"
    }

@app.route('/api/get', methods=['GET'])
def welcome():
    """Welcome endpoint."""
    return jsonify({
        "message": "Welcome to BNSAdvisor API - Updated for New Indian Criminal Justice System",
        "version": "2.0.0",
        "status": "operational",
        "legal_framework": {
            "updated": "July 2025",
            "new_codes": {
                "BNS": "Bharatiya Nyaya Sanhita (replaced IPC)",
                "BNSS": "Bharatiya Nagarik Suraksha Sanhita (replaced CrPC)",
                "BSA": "Bharatiya Sakshya Adhiniyam (replaced Evidence Act)"
            },
            "note": "All legal analysis now uses the NEW LEGAL CODES as primary reference with old code mapping for context"
        },
        "available_endpoints": [
            {
                "endpoint": "/api/get",
                "method": "GET",
                "description": "Get information about all available API endpoints and new legal framework",
                "parameters": []
            },
            {
                "endpoint": "/api/bns-advisor",
                "method": "POST",
                "description": "Get legal advice using BNS (Bharatiya Nyaya Sanhita), BNSS, and BSA frameworks with old code mapping",
                "parameters": [
                    {"name": "case_description", "type": "string", "required": True, "description": "Description of the legal case to analyze"},
                    {"name": "verify", "type": "boolean", "required": False, "default": True, "description": "Whether to verify legal references with supporting evidence"}
                ]
            },
            {
                "endpoint": "/api/legal-advisor",
                "method": "POST",
                "description": "Get comprehensive legal analysis using NEW LEGAL CODES (BNS/BNSS/BSA) with structured debate and advice",
                "parameters": [
                    {"name": "case_description", "type": "string", "required": True, "description": "Description of the legal case to analyze"},
                    {"name": "evidence", "type": "array", "required": False, "description": "List of evidence items to consider in the analysis"},
                    {"name": "verify", "type": "boolean", "required": False, "default": True, "description": "Whether to verify legal references with supporting evidence"},
                    {"name": "include_debate", "type": "boolean", "required": False, "default": False, "description": "Whether to include structured debate after comprehensive advice"}
                ]
            },
            {
                "endpoint": "/api/legal-research",
                "method": "POST",
                "description": "🔥 PREMIUM: Advanced AI-powered legal research using multiple search engines (Tavily + Google) with comprehensive data extraction and 10+ results per query",
                "parameters": [
                    {"name": "query", "type": "string", "required": True, "description": "The legal question or research query"},
                    {"name": "conversation_id", "type": "string", "required": False, "description": "ID of an existing conversation (for follow-ups)"}
                ],
                "features": [
                    "Multi-engine search (Tavily + Google)",
                    "10+ comprehensive results with full content",
                    "AI-powered analysis and insights",
                    "Authoritative legal sources",
                    "Single optimized search execution",
                    "Comprehensive data extraction"
                ]
            },
            {
                "endpoint": "/api/legal-research/follow-up",
                "method": "POST",
                "description": "Provide follow-up information to a legal research question using updated legal framework",
                "parameters": [
                    {"name": "conversation_id", "type": "string", "required": True, "description": "ID of the conversation to continue"},
                    {"name": "query", "type": "string", "required": True, "description": "The original legal question"},
                    {"name": "answer", "type": "string", "required": True, "description": "Answer to the current clarifying question"},
                    {"name": "question_number", "type": "integer", "required": True, "description": "The number of the question being answered (1-based)"}
                ]
            },
            {
                "endpoint": "/api/judgement-analyser",
                "method": "POST",
                "description": "Analyze court judgements using NEW LEGAL CODES (BNS/BNSS/BSA) with comprehensive assessment and old code mapping",
                "parameters": [
                    {"name": "judgement_details", "type": "string", "required": True, "description": "The judgement text to analyze"},
                    {"name": "crime_details", "type": "string", "required": False, "description": "Details of the alleged crime"},
                    {"name": "evidence_details", "type": "string", "required": False, "description": "Details of evidence presented"},
                    {"name": "verify", "type": "boolean", "required": False, "default": True, "description": "Whether to verify legal references with supporting evidence"},
                    {"name": "include_detailed_analysis", "type": "boolean", "required": False, "default": False, "description": "Whether to include detailed section-by-section analysis using BNS/BNSS/BSA"}
                ]
            },
            {
                "endpoint": "/api/judgement-analyser/upload",
                "method": "POST",
                "description": "🔥 ADVANCED: Analyze court judgements from PDF files using NEW LEGAL CODES (BNS/BNSS/BSA) with multi-engine extraction and comprehensive metadata",
                "parameters": [
                    {"name": "pdf_file", "type": "file", "required": True, "description": "PDF file containing the judgement to analyze"},
                    {"name": "verify", "type": "boolean", "required": False, "default": True, "description": "Whether to verify legal references with supporting evidence"},
                    {"name": "include_detailed_analysis", "type": "boolean", "required": False, "default": False, "description": "Whether to include detailed section-by-section analysis using BNS/BNSS/BSA"},
                    {"name": "case_number", "type": "string", "required": False, "description": "Optional case number to override extracted value"},
                    {"name": "petitioner", "type": "string", "required": False, "description": "Optional petitioner name to override extracted value"},
                    {"name": "respondent", "type": "string", "required": False, "description": "Optional respondent name to override extracted value"},
                    {"name": "date_of_judgement", "type": "string", "required": False, "description": "Optional date to override extracted value"},
                    {"name": "bench", "type": "string", "required": False, "description": "Optional bench information to override extracted value"},
                    {"name": "court", "type": "string", "required": False, "description": "Optional court information to override extracted value"},
                    {"name": "extract_only", "type": "boolean", "required": False, "default": False, "description": "If true, only extracts metadata without performing analysis"}
                ],
                "advanced_features": [
                    "🔥 Multi-engine PDF extraction (PyPDF2, PDFMiner, PDFPlumber, PyMuPDF)",
                    "🔥 Automatic quality assessment and best method selection",
                    "🔥 Advanced legal metadata extraction (acts, sections, citations)",
                    "🔥 Court level and judgment type classification",
                    "🔥 Comprehensive processing summary and quality metrics"
                ]
            },
            {
                "endpoint": "/api/judgement-analyser/validate",
                "method": "POST",
                "description": "🔥 NEW: Validate PDF content and get processing summary without full analysis",
                "parameters": [
                    {"name": "pdf_file", "type": "file", "required": True, "description": "PDF file to validate and analyze"}
                ],
                "returns": [
                    "PDF validation results",
                    "Processing quality metrics",
                    "Recommended processing method",
                    "Legal document confidence score",
                    "Advanced metadata preview"
                ]
            },
            {
                "endpoint": "/api/judgement-question",
                "method": "POST",
                "description": "Answer follow-up questions about previously analyzed judgements using NEW LEGAL CODES framework",
                "parameters": [
                    {"name": "question", "type": "string", "required": True, "description": "The follow-up question about the judgement"},
                    {"name": "analysis", "type": "string", "required": False, "description": "Previous analysis content (optional)"},
                    {"name": "judgement", "type": "string", "required": False, "description": "Original judgement text (optional)"}
                ]
            }
        ],
        "streaming_endpoints": [
            {
                "endpoint": "/api/bns-advisor/stream",
                "method": "POST",
                "description": "🔥 STREAMING: Real-time streaming BNS analysis with word-by-word response",
                "parameters": [
                    {"name": "case_description", "type": "string", "required": True, "description": "Description of the legal case to analyze"},
                    {"name": "verify", "type": "boolean", "required": False, "default": True, "description": "Whether to verify legal references"}
                ],
                "response_type": "text/plain (Server-Sent Events)",
                "streaming": True
            },
            {
                "endpoint": "/api/legal-advisor/stream",
                "method": "POST",
                "description": "🔥 STREAMING: Real-time streaming case analysis with immediate feedback",
                "parameters": [
                    {"name": "case_description", "type": "string", "required": True, "description": "Description of the legal case to analyze"},
                    {"name": "evidence", "type": "array", "required": False, "description": "List of evidence items"},
                    {"name": "verify", "type": "boolean", "required": False, "default": True, "description": "Whether to verify legal references"},
                    {"name": "include_debate", "type": "boolean", "required": False, "default": False, "description": "Whether to include debate"},
                    {"name": "chat_id", "type": "string", "required": False, "description": "Chat ID for session tracking"}
                ],
                "response_type": "text/plain (Server-Sent Events)",
                "streaming": True
            },
            {
                "endpoint": "/api/judgement-analyser/stream",
                "method": "POST",
                "description": "🔥 STREAMING: Real-time streaming judgement analysis with comprehensive insights",
                "parameters": [
                    {"name": "judgement_details", "type": "string", "required": True, "description": "Details of the court judgement"},
                    {"name": "crime_details", "type": "string", "required": False, "description": "Details of the crime"},
                    {"name": "evidence_details", "type": "string", "required": False, "description": "Details of evidence"},
                    {"name": "verify", "type": "boolean", "required": False, "default": True, "description": "Whether to verify legal references"},
                    {"name": "include_detailed_analysis", "type": "boolean", "required": False, "default": False, "description": "Whether to include detailed analysis"}
                ],
                "response_type": "text/plain (Server-Sent Events)",
                "streaming": True
            },
            {
                "endpoint": "/api/legal-research/stream",
                "method": "POST",
                "description": "🔥 STREAMING: Real-time streaming legal research with progressive results",
                "parameters": [
                    {"name": "query", "type": "string", "required": True, "description": "The legal research query"},
                    {"name": "conversation_id", "type": "string", "required": False, "description": "Conversation ID for context"},
                    {"name": "follow_up_info", "type": "object", "required": False, "description": "Follow-up information from previous questions"}
                ],
                "response_type": "text/plain (Server-Sent Events)",
                "streaming": True
            }
        ],
        "streaming_features": [
            "🔥 Real-time word-by-word response streaming",
            "🔥 Immediate feedback without waiting for complete analysis",
            "🔥 Progressive content delivery for better user experience",
            "🔥 Server-Sent Events (SSE) for reliable streaming",
            "🔥 Proper error handling for streaming interruptions",
            "🔥 Chunk-based content delivery with metadata"
        ],
        "documentation": "For more details on using these endpoints, refer to the documentation."
    })

@app.route('/api/bns-advisor', methods=['POST'])
def bns_advisor_endpoint():
    """BNSAdvisor endpoint."""
    data = request.json

    if not data or 'case_description' not in data:
        return jsonify({
            "status": "error",
            "message": "Missing required field: case_description"
        }), 400

    # Set verification flag (default to True)
    verify = data.get('verify', True)

    try:
        # Process the case
        result = bns_advisor_instance.analyze_case(data['case_description'], verify)

        # Extract the content from the result object
        response_content = ""

        if isinstance(result, dict):
            if "agent" in result and "messages" in result["agent"] and result["agent"]["messages"]:
                message = result["agent"]["messages"][-1]
                if hasattr(message, 'content'):
                    response_content = message.content
            elif "messages" in result and result["messages"]:
                message = result["messages"][-1]
                if hasattr(message, 'content'):
                    response_content = message.content
            elif "error" in result:
                response_content = result["error"]

        # Create a serializable response
        return jsonify({
            "status": "success",
            "analysis": response_content,
            "framework": "BNS/BNSS/BSA (July 2024)",
            "note": "Analysis uses NEW LEGAL CODES as primary reference with old code mapping for context"
        })

    except Exception as e:
        logger.error(f"Error in bns_advisor_endpoint: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"An error occurred: {str(e)}"
        }), 500

@app.route('/api/bns-advisor/stream', methods=['POST'])
def bns_advisor_stream_endpoint():
    """🔥 STREAMING: BNSAdvisor endpoint with real-time streaming response."""
    data = request.json

    if not data or 'case_description' not in data:
        return jsonify({
            "status": "error",
            "message": "Missing required field: case_description"
        }), 400

    # Set verification flag (default to True)
    verify = data.get('verify', True)

    def generate_bns_analysis():
        """Generator function for streaming BNS analysis."""
        try:
            # Send initial metadata
            yield {
                "type": "metadata",
                "data": {
                    "framework": "BNS/BNSS/BSA (July 2024)",
                    "case_description": data['case_description'][:100] + "..." if len(data['case_description']) > 100 else data['case_description'],
                    "verification_enabled": verify
                },
                "status": "starting"
            }

            # Use the streaming analysis method
            for chunk in bns_advisor_instance.stream_analysis(data['case_description']):
                if isinstance(chunk, dict):
                    if "agent" in chunk and "messages" in chunk["agent"] and chunk["agent"]["messages"]:
                        # Extract content from the last message
                        last_message = chunk["agent"]["messages"][-1]
                        if hasattr(last_message, 'content'):
                            content = last_message.content
                            # Stream the content in smaller chunks
                            for text_chunk in chunk_text(content, chunk_size=25):
                                yield {
                                    "type": "content",
                                    "data": text_chunk,
                                    "status": "streaming"
                                }
                    elif "messages" in chunk:
                        for message in chunk["messages"]:
                            if hasattr(message, 'content'):
                                content = message.content
                                for text_chunk in chunk_text(content, chunk_size=25):
                                    yield {
                                        "type": "content",
                                        "data": text_chunk,
                                        "status": "streaming"
                                    }
                    elif "error" in chunk:
                        yield {
                            "type": "error",
                            "data": chunk["error"],
                            "status": "error"
                        }
                        return

        except Exception as e:
            logger.error(f"Error in streaming BNS analysis: {str(e)}")
            yield {
                "type": "error",
                "data": f"An error occurred: {str(e)}",
                "status": "error"
            }

    return create_streaming_response(generate_bns_analysis)

@app.route('/api/legal-advisor', methods=['POST'])
def legal_advisor():
    """CaseAnalysis Legal Advisor endpoint with optional debate feature."""
    data = request.json

    if not data or 'case_description' not in data:
        return jsonify({
            "status": "error",
            "message": "Missing required field: case_description"
        }), 400

    # Optional evidence field
    evidence = data.get('evidence', None)

    # Set verification flag (default to True)
    verify = data.get('verify', True)

    # Set include_debate flag (default to False)
    include_debate = data.get('include_debate', False)

    # Get or generate chat_id for debate sessions
    chat_id = data.get('chat_id', None)
    if not chat_id:
        # Generate unique chat_id for this case
        chat_id = f"case_{uuid.uuid4().hex[:8]}_{int(datetime.datetime.now().timestamp())}"

    try:
        # Process the case with the new parameter
        result = case_analysis.analyze_case(data['case_description'], evidence, verify, include_debate)

        # Create a serializable response
        # Note: For CaseAnalysisAgent, analyze_case returns a string directly
        if isinstance(result, str):
            response_content = result
        else:
            # This is a fallback in case the return type changes
            response_content = "Analysis failed to produce valid content."

        # Prepare the response
        response = {
            "status": "success",
            "analysis": response_content,
            "framework": "BNS/BNSS/BSA (July 2024)",
            "note": "Analysis uses NEW LEGAL CODES with old code mapping for reference",
            "chat_id": chat_id  # Always include chat_id
        }

        # If debate was not included, ask if user wants it
        if not include_debate:
            response["debate_available"] = True
            response["debate_prompt"] = "Would you like to include a detailed legal debate analysis for this case?"
            response["debate_instruction"] = {
                "message": "To get debate analysis, send another request with:",
                "required_fields": {
                    "case_description": "same case description",
                    "include_debate": True,
                    "chat_id": chat_id  # Use the generated chat_id
                },
                "optional_fields": {
                    "evidence": "same evidence if provided",
                    "verify": "same verification flag"
                }
            }
        else:
            # If debate was included
            response["debate_included"] = True

        return jsonify(response)

    except Exception as e:
        logger.error(f"Error in legal_advisor: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"An error occurred: {str(e)}"
        }), 500

@app.route('/api/legal-advisor/stream', methods=['POST'])
def legal_advisor_stream_endpoint():
    """🔥 STREAMING: Legal Advisor endpoint with real-time streaming response."""
    data = request.json

    if not data or 'case_description' not in data:
        return jsonify({
            "status": "error",
            "message": "Missing required field: case_description"
        }), 400

    # Optional evidence field
    evidence = data.get('evidence', None)
    verify = data.get('verify', True)
    include_debate = data.get('include_debate', False)
    chat_id = data.get('chat_id', None)

    if not chat_id:
        chat_id = f"case_{uuid.uuid4().hex[:8]}_{int(datetime.datetime.now().timestamp())}"

    def generate_case_analysis():
        """Generator function for streaming case analysis."""
        try:
            # Send initial metadata
            yield {
                "type": "metadata",
                "data": {
                    "framework": "BNS/BNSS/BSA (July 2024)",
                    "case_description": data['case_description'][:100] + "..." if len(data['case_description']) > 100 else data['case_description'],
                    "verification_enabled": verify,
                    "debate_included": include_debate,
                    "chat_id": chat_id
                },
                "status": "starting"
            }

            # Process the case with streaming
            result = case_analysis.analyze_case(data['case_description'], evidence, verify, include_debate)

            if isinstance(result, str):
                # Stream the content in chunks
                for text_chunk in chunk_text(result, chunk_size=30):
                    yield {
                        "type": "content",
                        "data": text_chunk,
                        "status": "streaming"
                    }

                # Send final metadata
                if not include_debate:
                    yield {
                        "type": "metadata",
                        "data": {
                            "debate_available": True,
                            "debate_prompt": "Would you like to include a detailed legal debate analysis for this case?",
                            "chat_id": chat_id
                        },
                        "status": "streaming"
                    }
                else:
                    yield {
                        "type": "metadata",
                        "data": {
                            "debate_included": True,
                            "chat_id": chat_id
                        },
                        "status": "streaming"
                    }
            else:
                yield {
                    "type": "error",
                    "data": "Analysis failed to produce valid content.",
                    "status": "error"
                }

        except Exception as e:
            logger.error(f"Error in streaming case analysis: {str(e)}")
            yield {
                "type": "error",
                "data": f"An error occurred: {str(e)}",
                "status": "error"
            }

    return create_streaming_response(generate_case_analysis)

@app.route('/api/judgement-analyser', methods=['POST'])
def judgement_analyser_endpoint():
    """JudgementAnalyser endpoint."""
    data = request.json

    if not data or 'judgement_details' not in data:
        return jsonify({
            "status": "error",
            "message": "Missing required field: judgement_details"
        }), 400

    # Optional fields
    crime_details = data.get('crime_details', "")
    evidence_details = data.get('evidence_details', "")

    # Set verification flag (default to True)
    verify = data.get('verify', True)

    # Set include_detailed_analysis flag (default to False)
    include_detailed_analysis = data.get('include_detailed_analysis', False)

    try:
        # Process the judgement
        result = judgement_analyser.analyze_judgement(
            data['judgement_details'],
            crime_details,
            evidence_details,
            verify,
            metadata=None,
            include_detailed_analysis=include_detailed_analysis
        )

        # Extract the content based on the result type
        response_content = ""

        # Handle nested AIMessage objects
        if isinstance(result, dict):
            # Try to extract from agent messages structure first
            if "agent" in result and "messages" in result["agent"] and result["agent"]["messages"]:
                message = result["agent"]["messages"][-1]
                if hasattr(message, 'content'):
                    response_content = message.content
            # Try direct messages structure next
            elif "messages" in result and result["messages"]:
                message = result["messages"][-1]
                if hasattr(message, 'content'):
                    response_content = message.content
            # Try direct content fields
            elif "content" in result:
                response_content = result["content"]
            elif "analysis" in result:
                response_content = result["analysis"]
            elif "message" in result:
                response_content = result["message"]
            elif "error" in result:
                response_content = result["error"]
        elif isinstance(result, str):
            response_content = result
        # Handle direct AIMessage object (unlikely but possible)
        elif hasattr(result, 'content'):
            response_content = result.content

        # If still empty, return an error message
        if not response_content:
            response_content = "Failed to extract analysis content from the response."
            logger.warning("Empty response content from judgment analyser")

        # Generate suggested follow-up questions based on the analysis content
        suggested_questions = generate_suggested_questions(response_content, data['judgement_details'])

        # Store the analysis and judgement details in the session for follow-up questions
        # We'll use the analysis_id to reference this analysis in follow-up questions
        analysis_id = str(uuid.uuid4())

        # Simulate session storage (in a real application, this would be in a database or session store)
        # This would need to be implemented with a proper persistence mechanism in production
        app.config.setdefault('analysis_store', {})
        app.config['analysis_store'][analysis_id] = {
            'analysis': response_content,
            'judgement': data['judgement_details'],
            'timestamp': datetime.datetime.now().isoformat()
        }

        # Create a serializable response
        return jsonify({
            "status": "success",
            "analysis": response_content,
            "analysis_id": analysis_id,
            "suggested_questions": suggested_questions,
            "followup_available": True,
            "followup_endpoint": "/api/judgement-question",
            "framework": "BNS/BNSS/BSA (July 2024)",
            "note": "Analysis uses NEW LEGAL CODES as primary reference with old code mapping for context"
        })
    except Exception as e:
        logger.error(f"Error in judgement_analyser_endpoint: {str(e)}")
        logger.exception("Exception details:")
        return jsonify({
            "status": "error",
            "message": f"An error occurred: {str(e)}"
        }), 500

@app.route('/api/judgement-analyser/stream', methods=['POST'])
def judgement_analyser_stream_endpoint():
    """🔥 STREAMING: Judgement Analyser endpoint with real-time streaming response."""
    data = request.json

    if not data or 'judgement_details' not in data:
        return jsonify({
            "status": "error",
            "message": "Missing required field: judgement_details"
        }), 400

    # Optional fields
    crime_details = data.get('crime_details', None)
    evidence_details = data.get('evidence_details', None)
    verify = data.get('verify', True)
    include_detailed_analysis = data.get('include_detailed_analysis', False)

    def generate_judgement_analysis():
        """Generator function for streaming judgement analysis."""
        try:
            # Send initial metadata
            yield {
                "type": "metadata",
                "data": {
                    "framework": "BNS/BNSS/BSA (July 2024)",
                    "judgement_details": data['judgement_details'][:100] + "..." if len(data['judgement_details']) > 100 else data['judgement_details'],
                    "verification_enabled": verify,
                    "detailed_analysis": include_detailed_analysis
                },
                "status": "starting"
            }

            # Analyze the judgement
            result = judgement_analyser.analyze_judgement(
                data['judgement_details'],
                crime_details,
                evidence_details,
                verify,
                include_detailed_analysis=include_detailed_analysis
            )

            if isinstance(result, dict) and 'analysis' in result:
                analysis_content = result['analysis']

                # Stream the analysis content in chunks
                for text_chunk in chunk_text(analysis_content, chunk_size=30):
                    yield {
                        "type": "content",
                        "data": text_chunk,
                        "status": "streaming"
                    }

                # Generate analysis ID for follow-up questions
                analysis_id = str(uuid.uuid4())

                # Send suggested questions
                if 'suggested_questions' in result:
                    yield {
                        "type": "metadata",
                        "data": {
                            "suggested_questions": result['suggested_questions'],
                            "analysis_id": analysis_id,
                            "followup_available": True
                        },
                        "status": "streaming"
                    }
            else:
                yield {
                    "type": "error",
                    "data": "Analysis failed to produce valid content.",
                    "status": "error"
                }

        except Exception as e:
            logger.error(f"Error in streaming judgement analysis: {str(e)}")
            yield {
                "type": "error",
                "data": f"An error occurred: {str(e)}",
                "status": "error"
            }

    return create_streaming_response(generate_judgement_analysis)

@app.route('/api/judgement-analyser/upload', methods=['POST'])
def judgement_analyser_upload():
    """Analyze a judgement from PDF upload."""
    if 'pdf_file' not in request.files:
        return jsonify({
            "status": "error",
            "message": "No file part in the request"
        }), 400

    file = request.files['pdf_file']

    if file.filename == '':
        return jsonify({
            "status": "error",
            "message": "No file selected"
        }), 400

    if file and (file.filename.lower().endswith('.pdf')):
        try:
            # Read the PDF file as bytes
            pdf_bytes = file.read()

            # 🔥 ADVANCED: Process PDF with enhanced capabilities
            sections = None
            processing_summary = None
            try:
                from judgementAnalyser import PDFProcessor

                # Get comprehensive processing summary
                processing_summary = PDFProcessor.get_processing_summary(pdf_bytes)
                logger.info(f"PDF Processing Summary: Quality={processing_summary.get('extraction_quality', 0):.2f}, Method={processing_summary.get('recommended_method', 'auto')}")

                # Process the PDF with advanced metadata extraction
                sections = PDFProcessor.process_pdf_judgement(pdf_bytes)
                logger.info("Successfully extracted sections from PDF with advanced metadata")

            except Exception as pdf_error:
                logger.error(f"Error in PDF processing: {str(pdf_error)}")
                sections = {}
                processing_summary = {"error": str(pdf_error)}

            # Extract basic metadata from sections (backward compatibility)
            extracted_metadata = {
                "case_number": sections.get("case_number", ""),
                "petitioner": sections.get("petitioner", ""),
                "respondent": sections.get("respondent", ""),
                "date_of_judgement": sections.get("date_of_judgement", ""),
                "date_of_filing": sections.get("date_of_filing", ""),
                "bench": sections.get("bench", ""),
                "court": sections.get("court", ""),
                "case_type": sections.get("case_type", ""),
                "subject_matter": sections.get("subject_matter", "")
            }

            # 🔥 NEW: Advanced metadata from enhanced processing
            advanced_metadata = {
                "legal_acts_mentioned": sections.get("legal_acts_mentioned", []),
                "sections_cited": sections.get("sections_cited", []),
                "case_citations": sections.get("case_citations", []),
                "judgment_type": sections.get("judgment_type", ""),
                "court_level": sections.get("court_level", ""),
                "pdf_metadata": sections.get("pdf_metadata", {}),
                "text_length": sections.get("text_length", 0),
                "extraction_quality": sections.get("extraction_quality", 0.0),
                "processing_timestamp": sections.get("processing_timestamp", "")
            }

            # Check if user wants to just extract metadata without analysis
            extract_only = request.form.get('extract_only', 'false').lower() == 'true'

            if extract_only:
                # 🔥 ENHANCED: Return comprehensive metadata and processing info without analysis
                return jsonify({
                    "status": "success",
                    "message": "PDF processed successfully with advanced metadata extraction.",
                    "extracted_metadata": extracted_metadata,
                    "advanced_metadata": advanced_metadata,
                    "processing_summary": processing_summary,
                    "extracted_text": {
                        "judgement_details": sections.get("judgement_details", ""),
                        "crime_details": sections.get("crime_details", ""),
                        "evidence_details": sections.get("evidence_details", ""),
                        "legal_reasoning": sections.get("legal_reasoning", ""),
                        "precedents_cited": sections.get("precedents_cited", ""),
                        "final_order": sections.get("final_order", "")
                    },
                    "quality_metrics": {
                        "extraction_quality": sections.get("extraction_quality", 0.0),
                        "text_length": sections.get("text_length", 0),
                        "recommended_method": processing_summary.get("recommended_method", "auto") if processing_summary else "auto"
                    }
                })

            # Override metadata if provided in the form
            metadata = {}
            if 'case_number' in request.form and request.form['case_number']:
                metadata['case_number'] = request.form['case_number']
            else:
                metadata['case_number'] = extracted_metadata['case_number']

            if 'petitioner' in request.form and request.form['petitioner']:
                metadata['petitioner'] = request.form['petitioner']
            else:
                metadata['petitioner'] = extracted_metadata['petitioner']

            if 'respondent' in request.form and request.form['respondent']:
                metadata['respondent'] = request.form['respondent']
            else:
                metadata['respondent'] = extracted_metadata['respondent']

            if 'date_of_judgement' in request.form and request.form['date_of_judgement']:
                metadata['date_of_judgement'] = request.form['date_of_judgement']
            else:
                metadata['date_of_judgement'] = extracted_metadata['date_of_judgement']

            if 'bench' in request.form and request.form['bench']:
                metadata['bench'] = request.form['bench']
            else:
                metadata['bench'] = extracted_metadata['bench']

            if 'court' in request.form and request.form['court']:
                metadata['court'] = request.form['court']
            else:
                metadata['court'] = extracted_metadata['court']

            # Set verification flag
            verify = request.form.get('verify', 'true').lower() == 'true'

            # Set include_detailed_analysis flag
            include_detailed_analysis = request.form.get('include_detailed_analysis', 'false').lower() == 'true'

            # Now perform the full analysis with the extracted and potentially overridden metadata
            judgement_details = sections.get("judgement_details", "")
            crime_details = sections.get("crime_details", "")
            evidence_details = sections.get("evidence_details", "")

            # Process the judgement
            result = judgement_analyser.analyze_judgement(
                judgement_details,
                crime_details,
                evidence_details,
                verify,
                metadata=metadata,
                include_detailed_analysis=include_detailed_analysis
            )

            # Extract the content based on the result type
            response_content = ""

            # Now get the analysis content
            if isinstance(result, dict):
                # Try to extract from agent messages structure first
                if "agent" in result and "messages" in result["agent"] and result["agent"]["messages"]:
                    message = result["agent"]["messages"][-1]
                    if hasattr(message, 'content'):
                        response_content = message.content
                # Try direct messages structure next
                elif "messages" in result and result["messages"]:
                    message = result["messages"][-1]
                    if hasattr(message, 'content'):
                        response_content = message.content
                # Try direct content fields
                elif "content" in result:
                    response_content = result["content"]
                elif "analysis" in result:
                    response_content = result["analysis"]
                elif "message" in result:
                    response_content = result["message"]
                elif "error" in result:
                    response_content = result["error"]
            elif isinstance(result, str):
                response_content = result
            # Handle direct AIMessage object
            elif hasattr(result, 'content'):
                response_content = result.content

            # If still empty, return an error message
            if not response_content:
                response_content = "Failed to extract analysis content from the response."
                logger.warning("Empty response content from judgment analyser")

            # Generate suggested follow-up questions based on the analysis content
            suggested_questions = generate_suggested_questions(response_content, judgement_details)

            # Store the analysis and judgement details for follow-up questions
            analysis_id = str(uuid.uuid4())

            # Simulate session storage (in a real application, this would be in a database or session store)
            app.config.setdefault('analysis_store', {})
            app.config['analysis_store'][analysis_id] = {
                'analysis': response_content,
                'judgement': judgement_details,
                'timestamp': datetime.datetime.now().isoformat()
            }

            # 🔥 ENHANCED: Create comprehensive response with advanced metadata
            return jsonify({
                "status": "success",
                "message": "PDF analyzed successfully with advanced processing",
                "metadata": metadata,
                "advanced_metadata": advanced_metadata,
                "processing_summary": processing_summary,
                "analysis": response_content,
                "analysis_id": analysis_id,
                "suggested_questions": suggested_questions,
                "quality_metrics": {
                    "extraction_quality": sections.get("extraction_quality", 0.0),
                    "text_length": sections.get("text_length", 0),
                    "processing_method": processing_summary.get("recommended_method", "auto") if processing_summary else "auto",
                    "processing_time": processing_summary.get("processing_time", 0.0) if processing_summary else 0.0
                },
                "legal_insights": {
                    "acts_mentioned": len(advanced_metadata.get("legal_acts_mentioned", [])),
                    "sections_cited": len(advanced_metadata.get("sections_cited", [])),
                    "case_citations": len(advanced_metadata.get("case_citations", [])),
                    "judgment_type": advanced_metadata.get("judgment_type", ""),
                    "court_level": advanced_metadata.get("court_level", "")
                },
                "followup_available": True,
                "followup_endpoint": "/api/judgement-question",
                "framework": "BNS/BNSS/BSA (July 2024)",
                "note": "Analysis uses NEW LEGAL CODES with ADVANCED PDF processing and comprehensive metadata extraction"
            })
        except Exception as e:
            logger.error(f"Error processing PDF: {str(e)}")
            logger.exception("Exception details:")
            return jsonify({
                "status": "error",
                "message": f"Error processing PDF: {str(e)}"
            }), 500
    else:
        return jsonify({
            "status": "error",
            "message": "Invalid file format. Only PDF files are allowed."
        }), 400

@app.route('/api/judgement-analyser/validate', methods=['POST'])
def judgement_analyser_validate():
    """🔥 NEW: Validate PDF content and get processing summary without full analysis."""
    if 'pdf_file' not in request.files:
        return jsonify({
            "status": "error",
            "message": "No file part in the request"
        }), 400

    file = request.files['pdf_file']

    if file.filename == '':
        return jsonify({
            "status": "error",
            "message": "No file selected"
        }), 400

    if file and (file.filename.lower().endswith('.pdf')):
        try:
            # Read the PDF file as bytes
            pdf_bytes = file.read()

            # Get comprehensive processing summary
            from judgementAnalyser import PDFProcessor

            # Get validation results
            validation_result = PDFProcessor.validate_pdf_content(pdf_bytes)

            # Get processing summary
            processing_summary = PDFProcessor.get_processing_summary(pdf_bytes)

            # Get basic metadata
            pdf_metadata = PDFProcessor.get_pdf_metadata(pdf_bytes)

            # Get advanced metadata preview (without full text processing)
            try:
                text_sample = PDFProcessor.extract_text_from_pdf(pdf_bytes)[:2000]  # First 2000 chars
                advanced_metadata_preview = PDFProcessor.extract_advanced_metadata(text_sample)
            except Exception as e:
                logger.warning(f"Could not extract metadata preview: {e}")
                advanced_metadata_preview = {}

            return jsonify({
                "status": "success",
                "message": "PDF validation completed successfully",
                "validation": validation_result,
                "processing_summary": processing_summary,
                "pdf_metadata": pdf_metadata,
                "advanced_metadata_preview": advanced_metadata_preview,
                "recommendations": {
                    "is_suitable_for_analysis": validation_result.get("is_legal_document", False),
                    "confidence_score": validation_result.get("confidence_score", 0.0),
                    "recommended_processing": processing_summary.get("recommended_method", "auto"),
                    "estimated_quality": processing_summary.get("extraction_quality", 0.0)
                },
                "framework": "BNS/BNSS/BSA (July 2024)",
                "note": "Advanced PDF validation with multi-engine processing assessment"
            })

        except Exception as e:
            logger.error(f"Error validating PDF: {str(e)}")
            logger.exception("Exception details:")
            return jsonify({
                "status": "error",
                "message": f"Error validating PDF: {str(e)}"
            }), 500
    else:
        return jsonify({
            "status": "error",
            "message": "Invalid file format. Only PDF files are allowed."
        }), 400

@app.route('/api/judgement-question', methods=['POST'])
def judgement_question():
    """Answer follow-up questions about a judgement."""
    data = request.json

    if not data or 'question' not in data:
        return jsonify({
            "status": "error",
            "message": "Missing required field: question"
        }), 400

    # Check if analysis_id is provided - this is the preferred method for follow-up questions
    analysis_id = data.get('analysis_id', None)

    # These fields are optional - if analysis_id is provided, we'll use stored data instead
    analysis = None
    judgement = None

    # Retrieve stored analysis and judgement if analysis_id is provided
    if analysis_id:
        analysis_store = app.config.get('analysis_store', {})
        if analysis_id in analysis_store:
            stored_data = analysis_store[analysis_id]
            analysis = stored_data.get('analysis', '')
            judgement = stored_data.get('judgement', '')
        else:
            return jsonify({
                "status": "error",
                "message": f"Analysis ID '{analysis_id}' not found. The analysis may have expired."
            }), 404
    else:
        # Fallback to directly provided analysis and judgement
        analysis = data.get('analysis', '')
        judgement = data.get('judgement', '')

    try:
        result = judgement_analyser.answer_follow_up_question(
            data['question'],
            analysis,
            judgement
        )

        # Extract content if result is not already a string
        if not isinstance(result, str):
            if isinstance(result, dict) and ("content" in result or "answer" in result or "response" in result):
                response_content = result.get("content") or result.get("answer") or result.get("response")
            else:
                response_content = "Unable to extract answer from result."
        else:
            response_content = result

        # Generate new suggested follow-up questions based on the current question and answer
        suggested_questions = generate_suggested_questions_for_followup(data['question'], response_content, analysis)

        return jsonify({
            "status": "success",
            "answer": response_content,
            "suggested_questions": suggested_questions,
            "analysis_id": analysis_id  # Return the same analysis_id for continuity
        })
    except Exception as e:
        logger.error(f"Error in judgement_question: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"An error occurred: {str(e)}"
        }), 500

@app.route('/api/legal-research', methods=['POST'])
def legal_research_endpoint():
    """🔥 PREMIUM Legal Research endpoint - Advanced AI-powered multi-engine search."""
    data = request.json

    if not data or 'query' not in data:
        return jsonify({
            "status": "error",
            "message": "Missing required field: query"
        }), 400

    # Optional conversation_id field
    conversation_id = data.get('conversation_id', None)

    try:
        # Process the query with our enhanced system
        result = legal_research.process_query(data['query'], conversation_id=conversation_id)

        # Check if the response is complete or needs clarification
        if not result.get('is_complete', False):
            # Return response with clarifying questions
            current_question = result.get('current_question', '')
            question_number = result.get('question_number', 0)
            total_questions = result.get('total_questions', 0)

            return jsonify({
                "status": "needs_clarification",
                "message": "Additional information needed to provide an accurate answer",
                "current_question": current_question,
                "question_number": question_number,
                "total_questions": total_questions,
                "conversation_id": result.get('conversation_id'),
                "premium_features": {
                    "multi_engine_search": "Tavily + Google (10+ results)",
                    "comprehensive_data": "Full content extraction",
                    "ai_analysis": "Advanced legal insights"
                }
            })
        else:
            # Process references to extract links with enhanced data
            references = result.get('references', [])
            links = []
            search_engines_used = []
            total_results = 0

            # Extract enhanced search information
            if 'search_data' in result:
                search_data = result['search_data']
                search_engines_used = search_data.get('search_engines_used', [])
                total_results = len(search_data.get('combined_results', []))

            # Extract links from references with source information
            for ref in references:
                if isinstance(ref, dict):
                    if 'url' in ref:
                        links.append({
                            'url': ref['url'],
                            'title': ref.get('citation', 'Reference'),
                            'source': ref.get('source', 'Unknown'),
                            'type': ref.get('type', 'general')
                        })
                    elif 'citation' in ref and ref['citation'].startswith('http'):
                        links.append({
                            'url': ref['citation'],
                            'title': 'Reference',
                            'source': ref.get('source', 'Unknown'),
                            'type': ref.get('type', 'general')
                        })

            # Return complete response with enhanced data
            return jsonify({
                "status": "success",
                "answer": result.get('answer', ''),
                "references": references,
                "links": links,
                "follow_up_suggestions": result.get('follow_up_suggestions', []),
                "conversation_id": result.get('conversation_id'),
                "premium_analytics": {
                    "search_engines_used": search_engines_used,
                    "total_results_found": total_results,
                    "total_links": len(links),
                    "search_quality": "Premium multi-engine comprehensive search",
                    "features_used": [
                        "Tavily AI Search",
                        "Google Custom Search (10+ results)",
                        "Comprehensive data extraction",
                        "AI-powered legal analysis"
                    ]
                },
                "framework": "BNS/BNSS/BSA (July 2024) + Enhanced Search",
                "note": "Premium legal research with multi-engine search and comprehensive data extraction"
            })
    except Exception as e:
        logger.error(f"Error in legal_research_endpoint: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"An error occurred: {str(e)}"
        }), 500

@app.route('/api/legal-research/stream', methods=['POST'])
def legal_research_stream_endpoint():
    """🔥 STREAMING: Legal Research endpoint with real-time streaming response."""
    data = request.json

    if not data or 'query' not in data:
        return jsonify({
            "status": "error",
            "message": "Missing required field: query"
        }), 400

    # Optional conversation_id field
    conversation_id = data.get('conversation_id', None)

    def generate_legal_research():
        """Generator function for streaming legal research."""
        try:
            # Send initial metadata
            yield {
                "type": "metadata",
                "data": {
                    "framework": "BNS/BNSS/BSA (July 2024) + Enhanced Search",
                    "query": data['query'][:100] + "..." if len(data['query']) > 100 else data['query'],
                    "conversation_id": conversation_id,
                    "search_engines": ["Tavily AI Search", "Google Custom Search"]
                },
                "status": "starting"
            }

            # Process the query
            result = legal_research.process_query(data['query'], conversation_id=conversation_id)

            # Check if the response is complete or needs clarification
            if not result.get('is_complete', False):
                # Stream clarification request
                yield {
                    "type": "clarification",
                    "data": {
                        "current_question": result.get('current_question', ''),
                        "question_number": result.get('question_number', 0),
                        "total_questions": result.get('total_questions', 0),
                        "conversation_id": result.get('conversation_id')
                    },
                    "status": "needs_clarification"
                }
            else:
                # Stream the complete answer
                answer = result.get('answer', '')
                for text_chunk in chunk_text(answer, chunk_size=35):
                    yield {
                        "type": "content",
                        "data": text_chunk,
                        "status": "streaming"
                    }

                # Stream references and metadata
                references = result.get('references', [])
                if references:
                    yield {
                        "type": "references",
                        "data": {
                            "references": references[:10],  # Limit to first 10
                            "total_references": len(references)
                        },
                        "status": "streaming"
                    }

                # Stream follow-up suggestions
                follow_up_suggestions = result.get('follow_up_suggestions', [])
                if follow_up_suggestions:
                    yield {
                        "type": "suggestions",
                        "data": {
                            "follow_up_suggestions": follow_up_suggestions,
                            "conversation_id": result.get('conversation_id')
                        },
                        "status": "streaming"
                    }

        except Exception as e:
            logger.error(f"Error in streaming legal research: {str(e)}")
            yield {
                "type": "error",
                "data": f"An error occurred: {str(e)}",
                "status": "error"
            }

    return create_streaming_response(generate_legal_research)

@app.route('/api/legal-research/follow-up', methods=['POST'])
def legal_research_follow_up():
    """Follow-up endpoint for the Legal Research assistant."""
    data = request.json

    if not data or 'conversation_id' not in data or 'query' not in data or 'answer' not in data or 'question_number' not in data:
        return jsonify({
            "status": "error",
            "message": "Missing required fields: conversation_id, query, answer, and question_number"
        }), 400

    try:
        # Create a dictionary with the question number as key and the answer as value
        follow_up_info = {
            str(data['question_number'] - 1): data['answer']
        }

        # Process the query with additional information
        result = legal_research.process_query(
            data['query'],
            follow_up_info=follow_up_info,
            conversation_id=data['conversation_id']
        )

        # Check if the response is complete or needs more clarification
        if not result.get('is_complete', False):
            # Return response with the next clarifying question
            current_question = result.get('current_question', '')
            question_number = result.get('question_number', 0)
            total_questions = result.get('total_questions', 0)

            return jsonify({
                "status": "needs_clarification",
                "message": "Additional information needed to provide an accurate answer",
                "current_question": current_question,
                "question_number": question_number,
                "total_questions": total_questions,
                "conversation_id": result.get('conversation_id')
            })
        else:
            # Process references to extract links
            references = result.get('references', [])
            links = []

            # Extract links from references
            for ref in references:
                if isinstance(ref, dict):
                    if 'url' in ref:
                        links.append({
                            'url': ref['url'],
                            'title': ref.get('citation', 'Reference')
                        })
                    elif 'citation' in ref and ref['citation'].startswith('http'):
                        links.append({
                            'url': ref['citation'],
                            'title': 'Reference'
                        })

            # The response is now complete
            return jsonify({
                "status": "success",
                "answer": result.get('answer', ''),
                "references": references,
                "links": links,
                "follow_up_suggestions": result.get('follow_up_suggestions', []),
                "conversation_id": result.get('conversation_id')
            })
    except Exception as e:
        logger.error(f"Error in legal_research_follow_up: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"An error occurred: {str(e)}"
        }), 500

# Helper function to generate suggested follow-up questions
def generate_suggested_questions(analysis_content, judgement_text):
    """Generate suggested follow-up questions based on analysis content."""
    try:
        # Use the legal research agent's LLM to generate questions if available
        if hasattr(legal_research, 'agent') and hasattr(legal_research.agent, 'llm'):
            try:
                from langchain_core.messages import SystemMessage, HumanMessage
                from langchain_core.prompts import ChatPromptTemplate
                from langchain_core.output_parsers import JsonOutputParser

                # Create a prompt for generating questions
                prompt = ChatPromptTemplate.from_messages([
                    SystemMessage(content="""You are a legal assistant helping to generate relevant follow-up questions about a legal judgment.
                    Based on the judgment analysis provided, suggest 5 specific follow-up questions that would help the user
                    understand the judgment better or explore important aspects that might need clarification.

                    Return ONLY the 5 questions in a JSON array format like this:
                    ["Question 1?", "Question 2?", "Question 3?", "Question 4?", "Question 5?"]

                    Do not include any explanations, introductions, or other text."""),
                    HumanMessage(content=f"""
                    JUDGMENT ANALYSIS:
                    {analysis_content[:1500]}... [truncated for brevity]

                    Generate 5 relevant follow-up questions about this judgment.
                    """)
                ])

                # Generate questions
                chain = prompt | legal_research.agent.llm | JsonOutputParser()
                questions = chain.invoke({})

                if isinstance(questions, list) and len(questions) > 0:
                    return questions[:5]  # Return up to 5 questions
            except Exception as e:
                logger.error(f"Error generating dynamic questions: {e}")
                # Fall through to default questions

        # Default questions if LLM generation fails or is unavailable
        return [
            "What are the key legal principles applied in this judgement?",
            "What are the strongest grounds for appeal in this case?",
            "How does this judgement compare to similar precedents?",
            "What evidence was most crucial in determining the outcome?",
            "What procedural errors might have occurred in this case?"
        ]
    except Exception as e:
        logger.error(f"Error generating suggested questions: {e}")
        return ["What are the key legal issues in this case?",
                "What are the possible grounds for appeal?"]

# Helper function to generate follow-up questions for subsequent interactions
def generate_suggested_questions_for_followup(current_question, answer, analysis=None):
    """Generate new suggested questions based on the current interaction."""
    try:
        # Use the legal research agent's LLM to generate questions if available
        if hasattr(legal_research, 'agent') and hasattr(legal_research.agent, 'llm'):
            try:
                from langchain_core.messages import SystemMessage, HumanMessage
                from langchain_core.prompts import ChatPromptTemplate
                from langchain_core.output_parsers import JsonOutputParser

                # Create a prompt for generating follow-up questions
                prompt = ChatPromptTemplate.from_messages([
                    SystemMessage(content="""You are a legal assistant helping to generate relevant follow-up questions.
                    Based on the question that was asked and the answer provided, suggest 5 new follow-up questions
                    that would help the user explore this legal topic further or get more specific information.

                    Return ONLY the 5 questions in a JSON array format like this:
                    ["Question 1?", "Question 2?", "Question 3?", "Question 4?", "Question 5?"]

                    Do not include any explanations, introductions, or other text."""),
                    HumanMessage(content=f"""
                    PREVIOUS QUESTION: {current_question}

                    ANSWER PROVIDED:
                    {answer[:1000]}... [truncated for brevity]

                    Generate 5 new follow-up questions based on this interaction.
                    """)
                ])

                # Generate questions
                chain = prompt | legal_research.agent.llm | JsonOutputParser()
                questions = chain.invoke({})

                if isinstance(questions, list) and len(questions) > 0:
                    return questions[:5]  # Return up to 5 questions
            except Exception as e:
                logger.error(f"Error generating dynamic follow-up questions: {e}")
                # Fall through to default questions

        # Default questions if LLM generation fails or is unavailable
        return [
            "Can you explain the legal reasoning in more detail?",
            "What precedents support this interpretation?",
            "How might the opposing party counter this argument?",
            "What additional evidence would strengthen this position?",
            "What would be the procedural next steps in this case?"
        ]
    except Exception as e:
        logger.error(f"Error generating follow-up questions: {e}")
        return ["Could you elaborate on that point?",
                "What are the implications of this for the case?"]

# Run the application
if __name__ == '__main__':
    # Check if required packages are installed
    try:
        # These imports are already at the top of the file, but we check again
        # to make sure they're available when running the app
        logger.info("All required packages are installed.")
        logger.info("CORS is enabled for all API routes.")
        logger.info("Starting BNSAdvisor API...")
        # Increase timeout for all requests
        app.config['TIMEOUT'] = 30000  # 5 minutes
        app.run(debug=True, host='0.0.0.0', port=3000, threaded=True)
    except ImportError as e:
        logger.error(f"Missing required package: {e}")
        logger.error("Please install all required packages:")
        logger.error("pip install flask flask-cors")
