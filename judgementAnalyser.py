"""
JudgementAnalyser: Legal Judgement Analysis Framework

An agentic AI framework for comprehensive judgement analysis and validation,
specializing in Indian law with focus on the Bharatiya Nyaya Sanhita (BNS),
Bharatiya Nagarik Suraksha <PERSON>hita (BNSS), and Bharatiya Sakshya <PERSON>hin<PERSON>m (BSA).

This framework evaluates legal judgements for validity, offers guidance to both
parties, and provides actionable recommendations with supporting evidence.
"""

import os
import logging
import json
import datetime
from typing import Dict, List, Any, Optional, Union
import re
import io


# PDF processing imports
import PyPDF2
import pdfminer.high_level
import pdfplumber
from tempfile import NamedTemporaryFile
import base64
import hashlib
from pathlib import Path
import datetime

# 🔥 ADVANCED: Handle PyMuPDF import gracefully
try:
    import fitz  # PyMuPDF
    PYMUPDF_AVAILABLE = True
    print("✅ PyMuPDF (fitz) loaded successfully")
except ImportError as e:
    print(f"⚠️  PyMuPDF not available: {e}")
    print("📝 Will use alternative PDF processing methods")
    PYMUPDF_AVAILABLE = False
    fitz = None

# LangChain imports
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain_core.runnables import Runnable, RunnableConfig
from langchain_core.output_parsers import StrOutputParser, JsonOutputParser
from langchain_core.tools import BaseTool, StructuredTool, Tool

# LLM providers
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_openai import ChatOpenAI
from langchain_groq import ChatGroq

# Search tools

from langchain_tavily import TavilySearch
from langchain_google_community import GoogleSearchAPIWrapper

# Agent frameworks
from langgraph.graph import END, StateGraph
from langgraph.prebuilt import create_react_agent
from langgraph.checkpoint.memory import MemorySaver

# Configure logging - only show errors
logging.basicConfig(level=logging.ERROR, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("JudgementAnalyser")

# Get API keys from environment variables and strip quotes if present
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "").strip('"')
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "").strip('"')
TAVILY_API_KEY = os.getenv("TAVILY_API_KEY", "").strip('"')
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY", "").strip('"')
GOOGLE_CSE_ID = "465258ebeb5384150"
GROQ_API_KEY = "********************************************************"

# Debug: Print API key status (first few characters only for security)
print(f"🔑 Judgement Analyser API Keys Status:")
print(f"   TAVILY_API_KEY: {'✅ Found' if TAVILY_API_KEY else '❌ Missing'} ({TAVILY_API_KEY[:8]}... if found)")
print(f"   GOOGLE_API_KEY: {'✅ Found' if GOOGLE_API_KEY else '❌ Missing'} ({GOOGLE_API_KEY[:8]}... if found)")
print(f"   GOOGLE_CSE_ID: {'✅ Found' if GOOGLE_CSE_ID else '❌ Missing'} ({GOOGLE_CSE_ID[:8]}... if found)")
print(f"   GROQ_API_KEY: {'✅ Found' if GROQ_API_KEY else '❌ Missing'} ({GROQ_API_KEY[:8]}... if found)")

# Set environment variables explicitly for any libraries that still look for them
os.environ["GOOGLE_API_KEY"] = GOOGLE_API_KEY or ""
os.environ["GOOGLE_CSE_ID"] = GOOGLE_CSE_ID or ""
os.environ["TAVILY_API_KEY"] = TAVILY_API_KEY or ""
os.environ["GROQ_API_KEY"] = GROQ_API_KEY or ""


class JudgementQuery:
    """Class for judgement queries submitted to the system."""
    
    def __init__(self, judgement_details, crime_details=None, evidence_details=None, 
                 jurisdiction="india", language="english", metadata=None):
        """
        Initialize a judgement query.
        
        Args:
            judgement_details: The judgement issued by the court
            crime_details: Details of the alleged crime
            evidence_details: Details of evidence presented
            jurisdiction: Legal jurisdiction (india, international, etc.)
            language: Language of the query (english, hindi, etc.)
            metadata: Dictionary with case metadata (case_number, bench, etc.)
        """
        self.judgement_details = judgement_details
        self.crime_details = crime_details or ""
        self.evidence_details = evidence_details or ""
        self.jurisdiction = jurisdiction
        self.language = language
        self.metadata = metadata or {}


class JudgementResponse:
    """Class for structured judgement analysis responses."""
    
    def __init__(self, validity_assessment, rating=None, alternative_judgement=None, 
                 accused_guidance=None, petitioner_guidance=None, conclusion=None, 
                 legal_references=None, supporting_evidence=None):
        """
        Initialize a judgement response.
        
        Args:
            validity_assessment: Assessment of the judgement's validity
            rating: Numerical rating of the judgement (if valid)
            alternative_judgement: Proposed alternative judgement (if invalid)
            accused_guidance: Guidance for the accused party
            petitioner_guidance: Guidance for the petitioner
            conclusion: Summary of findings and recommendations
            legal_references: Legal references cited in the analysis
            supporting_evidence: Supporting evidence for the analysis
        """
        self.validity_assessment = validity_assessment
        self.rating = rating
        self.alternative_judgement = alternative_judgement
        self.accused_guidance = accused_guidance or []
        self.petitioner_guidance = petitioner_guidance or []
        self.conclusion = conclusion
        self.legal_references = legal_references or []
        self.supporting_evidence = supporting_evidence or []


class LegalVerifier:
    """Verifies legal references with supporting evidence from search engines."""
    
    def __init__(self, search_tool=None):
        """
        Initialize the legal verifier.
        
        Args:
            search_tool: Search tool to use for finding supporting evidence
        """
        self.search_tool = search_tool
        if not self.search_tool:
            try:
                # Try to create Google search if not provided
                self.search_tool = GoogleSearchAPIWrapper(
                    google_api_key=GOOGLE_API_KEY, 
                    google_cse_id=GOOGLE_CSE_ID
                )
            except Exception as e:
                logger.warning(f"Could not initialize Google search for verification: {e}")
                self.search_tool = None
    
    def extract_legal_references(self, text):
        """
        Extract legal references from text.
        
        Args:
            text: Text to extract references from
            
        Returns:
            List of extracted legal references
        """
        # Enhanced patterns for NEW CODES (BNS, BNSS, BSA) and OLD CODES (IPC, CrPC, Evidence Act)
        patterns = [
            # New Codes - Primary patterns
            r'BNS\s+[Ss]ection\s+(\d+[A-Z]?(?:\(\d+\))?)',
            r'BNSS\s+[Ss]ection\s+(\d+[A-Z]?(?:\(\d+\))?)',
            r'BSA\s+[Ss]ection\s+(\d+[A-Z]?(?:\(\d+\))?)',
            r'Bharatiya\s+Nyaya\s+Sanhita\s+[Ss]ection\s+(\d+[A-Z]?(?:\(\d+\))?)',
            r'Bharatiya\s+Nagarik\s+Suraksha\s+Sanhita\s+[Ss]ection\s+(\d+[A-Z]?(?:\(\d+\))?)',
            r'Bharatiya\s+Sakshya\s+Adhiniyam\s+[Ss]ection\s+(\d+[A-Z]?(?:\(\d+\))?)',
            
            # Direct section references with code context
            r'[Ss]ection\s+(\d+[A-Z]?(?:\(\d+\))?)\s+(?:of\s+)?(?:the\s+)?(?:BNS|Bharatiya\s+Nyaya\s+Sanhita)',
            r'[Ss]ection\s+(\d+[A-Z]?(?:\(\d+\))?)\s+(?:of\s+)?(?:the\s+)?(?:BNSS|Bharatiya\s+Nagarik\s+Suraksha\s+Sanhita)',
            r'[Ss]ection\s+(\d+[A-Z]?(?:\(\d+\))?)\s+(?:of\s+)?(?:the\s+)?(?:BSA|Bharatiya\s+Sakshya\s+Adhiniyam)',
            
            # Old Codes - Secondary patterns (for reference/mapping)
            r'IPC\s+[Ss]ection\s+(\d+[A-Z]?(?:\(\d+\))?)',
            r'CrPC\s+[Ss]ection\s+(\d+[A-Z]?(?:\(\d+\))?)',
            r'Evidence\s+Act\s+[Ss]ection\s+(\d+[A-Z]?(?:\(\d+\))?)',
            r'Indian\s+Penal\s+Code\s+[Ss]ection\s+(\d+[A-Z]?(?:\(\d+\))?)',
            r'Criminal\s+Procedure\s+Code\s+[Ss]ection\s+(\d+[A-Z]?(?:\(\d+\))?)',
            
            # General section references
            r'[Ss]ection\s+(\d+[A-Z]?(?:\(\d+\))?)\s+(?:of\s+)?(?:the\s+)?(?:IPC|Indian\s+Penal\s+Code)',
            r'[Ss]ection\s+(\d+[A-Z]?(?:\(\d+\))?)\s+(?:of\s+)?(?:the\s+)?(?:CrPC|Criminal\s+Procedure\s+Code)',
            r'[Ss]ection\s+(\d+[A-Z]?(?:\(\d+\))?)\s+(?:of\s+)?(?:the\s+)?(?:Evidence\s+Act)',
            
            # PMLA sections
            r'PMLA\s+[Ss]ection\s+(\d+[A-Z]?(?:\(\d+\))?)',
            r'[Ss]ection\s+(\d+[A-Z]?(?:\(\d+\))?)\s+(?:of\s+)?(?:the\s+)?(?:PMLA|Prevention\s+of\s+Money\s+Laundering\s+Act)',
            
            # Short form references
            r'[Ss]\.\s*(\d+[A-Z]?(?:\(\d+\))?)\s+(?:of\s+)?(?:BNS|BNSS|BSA|IPC|CrPC|PMLA)',
        ]
        
        references = []
        for pattern in patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                # Extract the full match and the section number
                full_match = match.group(0)
                section_num = match.group(1)
                
                # Determine which code - prioritize NEW CODES
                code = "Unknown"
                code_type = "unknown"  # new, old, or other
                
                full_match_lower = full_match.lower()
                
                # NEW CODES (Primary)
                if any(term in full_match_lower for term in ["bns", "bharatiya nyaya sanhita"]):
                    code = "BNS"
                    code_type = "new"
                elif any(term in full_match_lower for term in ["bnss", "bharatiya nagarik suraksha sanhita"]):
                    code = "BNSS"
                    code_type = "new"
                elif any(term in full_match_lower for term in ["bsa", "bharatiya sakshya adhiniyam"]):
                    code = "BSA"
                    code_type = "new"
                # OLD CODES (Secondary)
                elif any(term in full_match_lower for term in ["ipc", "indian penal code"]):
                    code = "IPC"
                    code_type = "old"
                elif any(term in full_match_lower for term in ["crpc", "criminal procedure code"]):
                    code = "CrPC"
                    code_type = "old"
                elif any(term in full_match_lower for term in ["evidence act"]):
                    code = "Evidence Act"
                    code_type = "old"
                # OTHER CODES
                elif any(term in full_match_lower for term in ["pmla", "prevention of money laundering act"]):
                    code = "PMLA"
                    code_type = "other"
                
                # Add to references if not already present
                ref = {
                    "code": code, 
                    "section": section_num, 
                    "full_match": full_match,
                    "code_type": code_type
                }
                if ref not in references:
                    references.append(ref)
        
        return references
    
    def find_supporting_evidence(self, reference):
        """
        Find supporting evidence for a legal reference.
        
        Args:
            reference: Legal reference to find evidence for
            
        Returns:
            List of supporting evidence
        """
        if not self.search_tool:
            logger.warning("Search tool unavailable for finding supporting evidence")
            return []
        
        try:
            # Construct search query
            code = reference["code"]
            section = reference["section"]
            query = f"{code} Section {section} Indian legal code reference"
            
            # Perform search
            search_results = self.search_tool.run(query)
            
            # Parse results
            evidence = []
            if isinstance(search_results, str):
                # Extract URLs from text results
                urls = re.findall(r'https?://[^\s]+', search_results)
                titles = re.findall(r'(?:^|\n)([^.\n]+)(?=\.)', search_results)
                
                for i, url in enumerate(urls[:3]):  # Limit to top 3
                    title = titles[i] if i < len(titles) else f"Reference for {code} Section {section}"
                    if title and url and "error" not in title.lower():
                        evidence.append({"title": title, "link": url})
            else:
                # Handle structured results
                for item in search_results[:3]:  # Limit to top 3
                    title = item.get("title", "")
                    link = item.get("link", "")
                    if title and link and "error" not in title.lower():
                        evidence.append({"title": title, "link": link})
            
            return evidence
        except Exception as e:
            logger.error(f"Error finding supporting evidence for {reference.get('code', 'unknown')} Section {reference.get('section', 'unknown')}: {e}")
            return []

    def verify_analysis(self, analysis_text):
        """
        Verify legal analysis with supporting evidence.
        
        Args:
            analysis_text: Legal analysis text to verify
            
        Returns:
            Original text with supporting evidence
        """
        # Extract references
        references = self.extract_legal_references(analysis_text)
        
        if not references:
            return analysis_text
        
        # Find supporting evidence for each reference
        verification_text = "\n\n## Supporting Evidence\n\n"
        evidence_found = False
        
        for ref in references:
            code = ref["code"]
            section = ref["section"]
            
            evidence = self.find_supporting_evidence(ref)
            if evidence:
                for item in evidence:
                    if item.get('title') and item.get('link') and item['title'] != f"Reference for {code} Section {section}":
                        verification_text += f"- **{code} Section {section}**: [{item['title']}]({item['link']})\n"
                        evidence_found = True
        
        # Only add the Supporting Evidence section if we found actual evidence
        if evidence_found:
            return analysis_text + verification_text
        else:
            return analysis_text


class LLMProvider:
    """Interface for different LLM providers."""
    
    @staticmethod
    def get_llm(provider: str = "gemini", model: Optional[str] = None, temperature: float = 0) -> Runnable:
        """Factory method to get an LLM based on provider."""
        try:
            logger.info(f"Initializing {provider} LLM model")
            
            if provider == "gemini":
                model_name = model or "gemini-2.5-pro"
                logger.info(f"Using Gemini model: {model_name}")
                return ChatGoogleGenerativeAI(
                    model=model_name, 
                    api_key=GEMINI_API_KEY, 
                    temperature=temperature,
                    request_timeout=30  # 30 second timeout
                )
            
            elif provider == "groq":
                model_name = model or "qwen/qwen3-32b"
                logger.info(f"Using Groq model: {model_name}")
                return ChatGroq(
                    model=model_name, 
                    api_key=GROQ_API_KEY, 
                    temperature=temperature,
                    request_timeout=30  # 30 second timeout
                )
            
            else:
                logger.warning(f"Unknown provider {provider}, defaulting to Gemini")
                return ChatGoogleGenerativeAI(
                    model="gemini-2.0-flash", 
                    api_key=GEMINI_API_KEY, 
                    temperature=temperature,
                    request_timeout=30  # 30 second timeout
                )
                
        except Exception as e:
            error_msg = f"Error initializing LLM ({provider}): {str(e)}"
            logger.error(error_msg)
            logger.exception("Detailed exception information:")
            print(f"ERROR: {error_msg}")
            
            # Fallback to a default LLM
            try:
                logger.info("Attempting fallback to Gemini")
                return ChatGoogleGenerativeAI(
                    model="gemini-2.5-pro", 
                    api_key=GEMINI_API_KEY, 
                    temperature=temperature,
                    request_timeout=30  # 30 second timeout
                )
            except Exception as fallback_error:
                error_msg = f"Fallback LLM initialization also failed: {str(fallback_error)}"
                logger.error(error_msg)
                raise ValueError(error_msg)


class EnhancedJudgementAnalysisSearchFactory:
    """🔥 PREMIUM Enhanced Factory for creating advanced judgement analysis search tools."""

    @staticmethod
    def create_enhanced_tavily_search(max_results: int = 10) -> BaseTool:
        """Create an enhanced Tavily search tool for judgement analysis."""
        print("🟡 Initializing Enhanced Tavily Search for Judgement Analysis...")
        if not TAVILY_API_KEY:
            print("   ❌ Tavily API key not found")
            logger.warning("Tavily API key not found")
            return None

        try:
            # Enhanced Tavily search with judgement analysis focus
            tavily_tool = TavilySearch(
                max_results=max_results,
                api_key=TAVILY_API_KEY,
                search_depth="advanced",
                include_answer=True,
                include_raw_content=True,
                include_images=False,
                include_domains=[
                    "indiankanoon.org", "sci.gov.in", "supremecourtofindia.nic.in",
                    "delhihighcourt.nic.in", "bombayhighcourt.nic.in", "mhc.tn.gov.in",
                    "advocatekhoj.com", "lawyersclubindia.com", "manupatra.com", "scconline.com"
                ]
            )
            print("   ✅ Enhanced Tavily search initialized for judgement analysis")
            return tavily_tool
        except Exception as e:
            print(f"   ❌ Tavily initialization failed: {e}")
            logger.error(f"Failed to initialize Tavily: {e}")
            return None
    
    @staticmethod
    def create_enhanced_google_search(max_results: int = 10) -> BaseTool:
        """Create an enhanced Google search tool for judgement analysis."""
        print("🌐 Initializing Enhanced Google Search for Judgement Analysis...")
        if not GOOGLE_API_KEY or not GOOGLE_CSE_ID:
            missing = []
            if not GOOGLE_API_KEY:
                missing.append("GOOGLE_API_KEY")
            if not GOOGLE_CSE_ID:
                missing.append("GOOGLE_CSE_ID")
            print(f"   ❌ Google search missing: {', '.join(missing)}")
            logger.warning(f"Google API credentials not found: {missing}")
            return None

        try:
            # Enhanced Google search for judgement analysis
            search = GoogleSearchAPIWrapper(
                google_api_key=GOOGLE_API_KEY,
                google_cse_id=GOOGLE_CSE_ID,
                k=max_results
            )

            google_tool = Tool(
                name="enhanced_judgement_google_search",
                description="🔥 PREMIUM: Enhanced Google search for comprehensive judgement analysis with 10+ results and precedent research.",
                func=search.run,
            )
            print("   ✅ Enhanced Google search initialized for judgement analysis")
            return google_tool
        except Exception as e:
            print(f"   ❌ Google initialization failed: {e}")
            logger.error(f"Failed to initialize Google search: {e}")
            return None

    @staticmethod
    def get_premium_judgement_analysis_tools() -> List[BaseTool]:
        """Get all available premium search tools for judgement analysis."""
        print("🔧 INITIALIZING PREMIUM JUDGEMENT ANALYSIS SEARCH TOOLS...")
        print("="*60)

        tools = []

        # Enhanced Tavily search
        tavily_tool = EnhancedJudgementAnalysisSearchFactory.create_enhanced_tavily_search(10)
        if tavily_tool:
            tools.append(tavily_tool)

        # Enhanced Google search
        google_tool = EnhancedJudgementAnalysisSearchFactory.create_enhanced_google_search(10)
        if google_tool:
            tools.append(google_tool)

        print(f"\n📊 PREMIUM JUDGEMENT ANALYSIS TOOLS INITIALIZATION COMPLETE:")
        print(f"   🎯 Successfully initialized: {len(tools)}/2 premium search engines")
        print(f"   🔍 Active premium tools: {[tool.name for tool in tools]}")
        print(f"   ⚖️ Judgement analysis optimized with precedent research")

        if len(tools) == 0:
            print("   ⚠️ WARNING: No search engines available! Judgement analysis will be limited.")
        else:
            print("   ✅ PREMIUM JUDGEMENT ANALYSIS SEARCH ENGINES READY!")

        print("="*60)
        return tools


class PDFProcessor:
    """🔥 ADVANCED PDF Processor for Legal Judgement Documents with multiple extraction methods."""

    @staticmethod
    def get_pdf_metadata(pdf_bytes):
        """Extract PDF metadata and document information."""
        try:
            pdf_file = io.BytesIO(pdf_bytes)
            pdf_reader = PyPDF2.PdfReader(pdf_file)

            metadata = {
                "num_pages": len(pdf_reader.pages),
                "file_size": len(pdf_bytes),
                "file_hash": hashlib.md5(pdf_bytes).hexdigest(),
                "creation_date": None,
                "modification_date": None,
                "title": None,
                "author": None,
                "subject": None,
                "creator": None,
                "producer": None
            }

            # Extract PDF metadata if available
            if pdf_reader.metadata:
                metadata.update({
                    "creation_date": str(pdf_reader.metadata.get('/CreationDate', '')),
                    "modification_date": str(pdf_reader.metadata.get('/ModDate', '')),
                    "title": str(pdf_reader.metadata.get('/Title', '')),
                    "author": str(pdf_reader.metadata.get('/Author', '')),
                    "subject": str(pdf_reader.metadata.get('/Subject', '')),
                    "creator": str(pdf_reader.metadata.get('/Creator', '')),
                    "producer": str(pdf_reader.metadata.get('/Producer', ''))
                })

            return metadata
        except Exception as e:
            logger.warning(f"Failed to extract PDF metadata: {e}")
            return {"error": str(e)}

    @staticmethod
    def extract_text_from_pdf(pdf_bytes):
        """
        🔥 ADVANCED: Extract text from PDF using multiple methods with quality scoring.

        Args:
            pdf_bytes: Raw bytes of the PDF file

        Returns:
            Best extracted text from the PDF with quality metrics
        """
        extraction_results = []

        # Method 1: PyPDF2
        try:
            text_pypdf2 = PDFProcessor._extract_with_pypdf2(pdf_bytes)
            quality_score = PDFProcessor._calculate_text_quality(text_pypdf2)
            extraction_results.append({
                "method": "PyPDF2",
                "text": text_pypdf2,
                "quality_score": quality_score,
                "length": len(text_pypdf2.strip())
            })
            logger.info(f"PyPDF2 extraction: {len(text_pypdf2)} chars, quality: {quality_score:.2f}")
        except Exception as e:
            logger.warning(f"PyPDF2 extraction failed: {e}")

        # Method 2: PDFMiner
        try:
            text_pdfminer = PDFProcessor._extract_with_pdfminer(pdf_bytes)
            quality_score = PDFProcessor._calculate_text_quality(text_pdfminer)
            extraction_results.append({
                "method": "PDFMiner",
                "text": text_pdfminer,
                "quality_score": quality_score,
                "length": len(text_pdfminer.strip())
            })
            logger.info(f"PDFMiner extraction: {len(text_pdfminer)} chars, quality: {quality_score:.2f}")
        except Exception as e:
            logger.warning(f"PDFMiner extraction failed: {e}")

        # Method 3: PDFPlumber (Advanced)
        try:
            text_pdfplumber = PDFProcessor._extract_with_pdfplumber(pdf_bytes)
            quality_score = PDFProcessor._calculate_text_quality(text_pdfplumber)
            extraction_results.append({
                "method": "PDFPlumber",
                "text": text_pdfplumber,
                "quality_score": quality_score,
                "length": len(text_pdfplumber.strip())
            })
            logger.info(f"PDFPlumber extraction: {len(text_pdfplumber)} chars, quality: {quality_score:.2f}")
        except Exception as e:
            logger.warning(f"PDFPlumber extraction failed: {e}")

        # Method 4: PyMuPDF (Most Advanced) - Only if available
        if PYMUPDF_AVAILABLE:
            try:
                text_pymupdf = PDFProcessor._extract_with_pymupdf(pdf_bytes)
                quality_score = PDFProcessor._calculate_text_quality(text_pymupdf)
                extraction_results.append({
                    "method": "PyMuPDF",
                    "text": text_pymupdf,
                    "quality_score": quality_score,
                    "length": len(text_pymupdf.strip())
                })
                logger.info(f"PyMuPDF extraction: {len(text_pymupdf)} chars, quality: {quality_score:.2f}")
            except Exception as e:
                logger.warning(f"PyMuPDF extraction failed: {e}")
        else:
            logger.info("PyMuPDF not available, skipping this extraction method")

        # Select best extraction result
        if not extraction_results:
            logger.error("All PDF extraction methods failed")
            return "Error: Unable to extract text from PDF using any method"

        # Sort by quality score (descending) and then by length (descending)
        best_result = max(extraction_results, key=lambda x: (x["quality_score"], x["length"]))

        logger.info(f"🏆 Best extraction method: {best_result['method']} "
                   f"(Quality: {best_result['quality_score']:.2f}, Length: {best_result['length']})")

        return best_result["text"]
    
    @staticmethod
    def _extract_with_pypdf2(pdf_bytes):
        """Extract text using PyPDF2."""
        text = ""
        try:
            pdf_file = io.BytesIO(pdf_bytes)
            pdf_reader = PyPDF2.PdfReader(pdf_file)

            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                page_text = page.extract_text()
                if page_text:
                    text += f"--- Page {page_num + 1} ---\n{page_text}\n\n"

            return text
        except Exception as e:
            logger.warning(f"PyPDF2 extraction failed: {e}")
            return ""

    @staticmethod
    def _extract_with_pdfminer(pdf_bytes):
        """Extract text using PDFMiner with better formatting."""
        try:
            pdf_file = io.BytesIO(pdf_bytes)
            text = pdfminer.high_level.extract_text(pdf_file)
            return text
        except Exception as e:
            logger.warning(f"PDFMiner extraction failed: {e}")
            return ""

    @staticmethod
    def _extract_with_pdfplumber(pdf_bytes):
        """🔥 ADVANCED: Extract text using PDFPlumber with table and layout preservation."""
        try:
            pdf_file = io.BytesIO(pdf_bytes)
            text = ""

            with pdfplumber.open(pdf_file) as pdf:
                for page_num, page in enumerate(pdf.pages):
                    text += f"--- Page {page_num + 1} ---\n"

                    # Extract text with better formatting
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n"

                    # Extract tables if present
                    tables = page.extract_tables()
                    if tables:
                        text += "\n[TABLES FOUND ON THIS PAGE]\n"
                        for table_num, table in enumerate(tables):
                            text += f"Table {table_num + 1}:\n"
                            for row in table:
                                if row:
                                    text += " | ".join([cell or "" for cell in row]) + "\n"
                            text += "\n"

                    text += "\n"

            return text
        except Exception as e:
            logger.warning(f"PDFPlumber extraction failed: {e}")
            return ""

    @staticmethod
    def _extract_with_pymupdf(pdf_bytes):
        """🔥 MOST ADVANCED: Extract text using PyMuPDF with OCR fallback and image extraction."""
        if not PYMUPDF_AVAILABLE or fitz is None:
            raise ImportError("PyMuPDF (fitz) is not available")

        try:
            pdf_file = io.BytesIO(pdf_bytes)
            doc = fitz.open(stream=pdf_file, filetype="pdf")
            text = ""

            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                text += f"--- Page {page_num + 1} ---\n"

                # Extract text
                page_text = page.get_text()
                if page_text.strip():
                    text += page_text + "\n"
                else:
                    # If no text found, try OCR on images
                    text += "[PAGE APPEARS TO BE IMAGE-BASED - OCR NEEDED]\n"
                    # Note: For full OCR, you'd need pytesseract
                    # pix = page.get_pixmap()
                    # img_data = pix.tobytes("png")
                    # ocr_text = pytesseract.image_to_string(Image.open(io.BytesIO(img_data)))
                    # text += ocr_text + "\n"

                # Extract images info
                image_list = page.get_images()
                if image_list:
                    text += f"[{len(image_list)} IMAGES FOUND ON THIS PAGE]\n"

                text += "\n"

            doc.close()
            return text
        except Exception as e:
            logger.warning(f"PyMuPDF extraction failed: {e}")
            return ""

    @staticmethod
    def _calculate_text_quality(text):
        """Calculate quality score for extracted text based on various metrics."""
        if not text or not text.strip():
            return 0.0

        text = text.strip()

        # Basic metrics
        length_score = min(len(text) / 1000, 1.0)  # Normalize to 1000 chars

        # Legal document indicators
        legal_keywords = [
            'court', 'judge', 'petitioner', 'respondent', 'case', 'section',
            'act', 'law', 'appeal', 'judgment', 'order', 'bench', 'coram',
            'criminal', 'civil', 'evidence', 'witness', 'accused', 'plaintiff',
            'defendant', 'bail', 'sentence', 'conviction', 'acquittal'
        ]

        legal_score = sum(1 for keyword in legal_keywords if keyword.lower() in text.lower()) / len(legal_keywords)

        # Structure indicators
        structure_patterns = [
            r'\d+\.',  # Numbered points
            r'[A-Z][a-z]+:',  # Headers with colons
            r'Section \d+',  # Legal sections
            r'Article \d+',  # Constitutional articles
            r'\([a-z]\)',  # Sub-points
        ]

        structure_score = sum(1 for pattern in structure_patterns if re.search(pattern, text)) / len(structure_patterns)

        # Readability (avoid garbled text)
        words = text.split()
        if words:
            avg_word_length = sum(len(word) for word in words) / len(words)
            readability_score = 1.0 if 3 <= avg_word_length <= 8 else 0.5
        else:
            readability_score = 0.0

        # Final weighted score
        quality_score = (
            length_score * 0.3 +
            legal_score * 0.4 +
            structure_score * 0.2 +
            readability_score * 0.1
        )

        return min(quality_score, 1.0)

    @staticmethod
    def extract_advanced_metadata(text):
        """🔥 ADVANCED: Extract comprehensive metadata from legal document text with section mapping."""
        metadata = {
            "legal_acts_mentioned": [],
            "sections_cited": [],
            "case_citations": [],
            "court_level": "",
            "judgment_type": "",
            "subject_matter": "",
            "section_mappings": {},  # 🔥 NEW: Store section mappings
            "legal_framework_analysis": {}  # 🔥 NEW: Analysis of legal framework used
        }

        # Extract legal acts mentioned
        act_patterns = [
            r'(Indian Penal Code|IPC)',
            r'(Bharatiya Nyaya Sanhita|BNS)',
            r'(Code of Criminal Procedure|CrPC)',
            r'(Bharatiya Nagarik Suraksha Sanhita|BNSS)',
            r'(Indian Evidence Act|Evidence Act)',
            r'(Bharatiya Sakshya Adhiniyam|BSA)',
            r'(Constitution of India|Constitutional)',
            r'(Prevention of Money Laundering Act|PMLA)',
            r'(Narcotic Drugs and Psychotropic Substances Act|NDPS)',
            r'(Information Technology Act|IT Act)',
            r'(Companies Act)',
            r'(Motor Vehicle Act|MV Act)',
            r'(Dowry Prohibition Act)',
            r'(Protection of Women from Domestic Violence Act)',
        ]

        for pattern in act_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                metadata["legal_acts_mentioned"].extend([match[0] if isinstance(match, tuple) else match for match in matches])

        # 🔥 ENHANCED: Extract section citations with comprehensive mapping
        # Create a temporary analyser instance to access mapping functions
        temp_analyser = JudgementAnalyserAgent()

        # Extract all sections with their mappings
        all_sections = temp_analyser.get_all_related_sections(text)

        # Store basic section citations (for backward compatibility)
        basic_section_patterns = [
            r'Section\s+(\d+[A-Z]*)',
            r'Article\s+(\d+[A-Z]*)',
            r'Rule\s+(\d+[A-Z]*)',
            r'Order\s+(\d+[A-Z]*)',
        ]

        for pattern in basic_section_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            metadata["sections_cited"].extend(matches)

        # 🔥 NEW: Store comprehensive section mappings
        metadata["section_mappings"] = all_sections

        # 🔥 NEW: Analyze legal framework usage
        framework_analysis = {
            "new_codes_used": len(all_sections["bns_sections"]) + len(all_sections["bnss_sections"]) + len(all_sections["bsa_sections"]),
            "old_codes_used": len(all_sections["ipc_sections"]) + len(all_sections["crpc_sections"]) + len(all_sections["evidence_act_sections"]),
            "total_sections": sum(len(sections) for sections in all_sections.values()),
            "framework_compliance": "new" if len(all_sections["bns_sections"]) + len(all_sections["bnss_sections"]) + len(all_sections["bsa_sections"]) > len(all_sections["ipc_sections"]) + len(all_sections["crpc_sections"]) + len(all_sections["evidence_act_sections"]) else "legacy",
            "mixed_framework": len(all_sections["bns_sections"]) > 0 and len(all_sections["ipc_sections"]) > 0
        }
        metadata["legal_framework_analysis"] = framework_analysis

        # Extract case citations
        case_citation_patterns = [
            r'([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\s+v\.?\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',
            r'(\d{4})\s+\((\d+)\)\s+([A-Z]+)\s+(\d+)',  # Year (Volume) Reporter Page
            r'AIR\s+(\d{4})\s+([A-Z]+)\s+(\d+)',  # AIR citations
        ]

        for pattern in case_citation_patterns:
            matches = re.findall(pattern, text)
            metadata["case_citations"].extend([' '.join(match) if isinstance(match, tuple) else match for match in matches])

        # Determine court level
        court_indicators = {
            "Supreme Court": ["Supreme Court", "SC", "Hon'ble Supreme Court"],
            "High Court": ["High Court", "HC", "Hon'ble High Court"],
            "District Court": ["District Court", "Sessions Court", "Magistrate"],
            "Tribunal": ["Tribunal", "NCLT", "NCLAT", "CAT"],
            "Consumer Court": ["Consumer", "NCDRC", "SCDRC"]
        }

        for court_type, indicators in court_indicators.items():
            if any(indicator.lower() in text.lower() for indicator in indicators):
                metadata["court_level"] = court_type
                break

        # Determine judgment type
        judgment_indicators = {
            "Criminal": ["criminal", "accused", "prosecution", "conviction", "acquittal", "bail", "FIR"],
            "Civil": ["civil", "plaintiff", "defendant", "damages", "injunction", "decree"],
            "Constitutional": ["constitutional", "fundamental rights", "writ", "mandamus", "certiorari"],
            "Commercial": ["commercial", "contract", "business", "trade", "arbitration"],
            "Family": ["family", "divorce", "custody", "maintenance", "matrimonial"],
            "Tax": ["tax", "income tax", "GST", "customs", "excise"],
            "Labour": ["labour", "employment", "industrial", "workman", "service"]
        }

        for judgment_type, indicators in judgment_indicators.items():
            if any(indicator.lower() in text.lower() for indicator in indicators):
                metadata["judgment_type"] = judgment_type
                break

        return metadata

    @staticmethod
    def validate_pdf_content(pdf_bytes):
        """🔥 ADVANCED: Validate PDF content and provide quality assessment."""
        try:
            validation_result = {
                "is_valid": False,
                "file_size": len(pdf_bytes),
                "is_legal_document": False,
                "confidence_score": 0.0,
                "issues": [],
                "recommendations": []
            }

            # Basic PDF validation
            try:
                pdf_file = io.BytesIO(pdf_bytes)
                pdf_reader = PyPDF2.PdfReader(pdf_file)
                validation_result["is_valid"] = True
                validation_result["num_pages"] = len(pdf_reader.pages)
            except Exception as e:
                validation_result["issues"].append(f"PDF parsing error: {str(e)}")
                return validation_result

            # Extract text for content analysis
            text = PDFProcessor.extract_text_from_pdf(pdf_bytes)

            if not text or len(text.strip()) < 50:
                validation_result["issues"].append("Insufficient text content extracted")
                validation_result["recommendations"].append("Document may be image-based, consider OCR processing")
                return validation_result

            # Check for legal document indicators
            legal_indicators = [
                'court', 'judge', 'petitioner', 'respondent', 'case', 'section',
                'judgment', 'order', 'appeal', 'criminal', 'civil', 'evidence',
                'accused', 'plaintiff', 'defendant', 'bench', 'coram'
            ]

            found_indicators = sum(1 for indicator in legal_indicators if indicator.lower() in text.lower())
            validation_result["confidence_score"] = min(found_indicators / len(legal_indicators), 1.0)
            validation_result["is_legal_document"] = validation_result["confidence_score"] > 0.3

            # Quality assessment
            if validation_result["confidence_score"] > 0.7:
                validation_result["recommendations"].append("High-quality legal document detected")
            elif validation_result["confidence_score"] > 0.4:
                validation_result["recommendations"].append("Moderate-quality legal document, may need manual review")
            else:
                validation_result["recommendations"].append("Low confidence - verify this is a legal judgment document")

            # File size assessment
            if validation_result["file_size"] > 10 * 1024 * 1024:  # 10MB
                validation_result["issues"].append("Large file size may affect processing speed")
            elif validation_result["file_size"] < 1024:  # 1KB
                validation_result["issues"].append("Very small file size, may be incomplete")

            return validation_result

        except Exception as e:
            return {
                "is_valid": False,
                "error": str(e),
                "recommendations": ["Unable to validate PDF content"]
            }

    @staticmethod
    def get_processing_summary(pdf_bytes):
        """🔥 ADVANCED: Get comprehensive processing summary for PDF."""
        try:
            summary = {
                "timestamp": datetime.datetime.now().isoformat(),
                "file_info": PDFProcessor.get_pdf_metadata(pdf_bytes),
                "validation": PDFProcessor.validate_pdf_content(pdf_bytes),
                "extraction_quality": 0.0,
                "processing_time": 0.0,
                "recommended_method": "auto"
            }

            start_time = datetime.datetime.now()

            # Test extraction quality
            text = PDFProcessor.extract_text_from_pdf(pdf_bytes)
            summary["extraction_quality"] = PDFProcessor._calculate_text_quality(text)

            end_time = datetime.datetime.now()
            summary["processing_time"] = (end_time - start_time).total_seconds()

            # Recommend processing method based on quality
            if summary["extraction_quality"] > 0.8:
                summary["recommended_method"] = "standard"
            elif summary["extraction_quality"] > 0.5:
                summary["recommended_method"] = "enhanced"
            else:
                summary["recommended_method"] = "ocr_required"

            return summary

        except Exception as e:
            return {
                "error": str(e),
                "timestamp": datetime.datetime.now().isoformat()
            }
    
    @staticmethod
    def process_pdf_judgement(pdf_bytes):
        """
        🔥 ADVANCED: Process a PDF judgment and extract its components with enhanced analysis.

        Args:
            pdf_bytes: Raw bytes of the PDF file

        Returns:
            A dictionary with extracted judgment components and metadata
        """
        # Get PDF metadata first
        pdf_metadata = PDFProcessor.get_pdf_metadata(pdf_bytes)

        # Extract text from PDF using best method
        full_text = PDFProcessor.extract_text_from_pdf(pdf_bytes)

        # Initialize the sections dictionary with enhanced metadata
        sections = {
            # Core content sections
            "judgement_details": "",
            "crime_details": "",
            "evidence_details": "",
            "legal_reasoning": "",
            "precedents_cited": "",
            "final_order": "",

            # Case metadata
            "case_number": "",
            "petitioner": "",
            "respondent": "",
            "date_of_judgement": "",
            "date_of_filing": "",
            "bench": "",
            "court": "",
            "case_type": "",
            "subject_matter": "",

            # Document metadata
            "pdf_metadata": pdf_metadata,
            "text_length": len(full_text),
            "extraction_quality": PDFProcessor._calculate_text_quality(full_text),
            "processing_timestamp": datetime.datetime.now().isoformat(),

            # Advanced analysis
            "legal_acts_mentioned": [],
            "sections_cited": [],
            "case_citations": [],
            "key_legal_points": [],
            "judgment_type": "",  # Civil/Criminal/Constitutional etc.
            "court_level": "",    # Supreme Court/High Court/District Court etc.
        }
        
        # 🔥 ADVANCED: Extract comprehensive metadata
        advanced_metadata = PDFProcessor.extract_advanced_metadata(full_text)
        sections.update(advanced_metadata)

        # Enhanced case metadata extraction with multiple patterns
        case_patterns = {
            "case_number": [
                r"(?:CASE|Appeal|Petition|Writ).*?(?:NO|No|Number)\.?:?\s*(.*?)(?:\n|$)",
                r"Case\s+No\.?\s*:?\s*(.*?)(?:\n|$)",
                r"Criminal\s+Appeal\s+No\.?\s*(.*?)(?:\n|$)",
                r"Civil\s+Appeal\s+No\.?\s*(.*?)(?:\n|$)",
                r"Writ\s+Petition\s+No\.?\s*(.*?)(?:\n|$)"
            ],
            "petitioner": [
                r"(?:PETITIONER|APPELLANT|PLAINTIFF):?\s*(.*?)(?:\n|$)",
                r"Petitioner\s*:?\s*(.*?)(?:\n|vs|v\.|$)",
                r"Appellant\s*:?\s*(.*?)(?:\n|vs|v\.|$)"
            ],
            "respondent": [
                r"(?:RESPONDENT|DEFENDANT):?\s*(.*?)(?:\n|$)",
                r"Respondent\s*:?\s*(.*?)(?:\n|$)",
                r"vs\.?\s+(.*?)(?:\n|$)",
                r"v\.?\s+(.*?)(?:\n|$)"
            ],
            "date_of_judgement": [
                r"(?:DATE\s+OF\s+JUDGMENT|DATE\s+OF\s+ORDER):?\s*(\d{1,2}/\d{1,2}/\d{2,4}|\d{1,2}\.\d{1,2}\.\d{2,4}|\d{1,2}-\d{1,2}-\d{2,4})",
                r"Decided\s+on:?\s*(\d{1,2}/\d{1,2}/\d{2,4}|\d{1,2}\.\d{1,2}\.\d{2,4}|\d{1,2}-\d{1,2}-\d{2,4})",
                r"Date\s+of\s+Decision:?\s*(\d{1,2}/\d{1,2}/\d{2,4}|\d{1,2}\.\d{1,2}\.\d{2,4}|\d{1,2}-\d{1,2}-\d{2,4})"
            ],
            "bench": [
                r"(?:BENCH|CORAM):?\s*(.*?)(?:\n|$)",
                r"(?:Before|Bench):?\s+([^.]+)",
                r"Hon'ble\s+(.*?)(?:\n|$)",
                r"Justice\s+(.*?)(?:\n|$)"
            ],
            "court": [
                r"IN\s+THE\s+(.*?COURT.*?)(?:\n|$)",
                r"(Supreme\s+Court\s+of\s+India)",
                r"(High\s+Court\s+of\s+.*?)(?:\n|$)",
                r"(District\s+Court\s+.*?)(?:\n|$)"
            ]
        }

        # Apply enhanced pattern matching
        for field, patterns in case_patterns.items():
            if not sections.get(field):  # Only if not already found
                for pattern in patterns:
                    match = re.search(pattern, full_text, re.IGNORECASE)
                    if match:
                        sections[field] = match.group(1).strip()
                        break
        
        # If no bench info found, look for judge names at the beginning of the document
        if not sections["bench"]:
            lines = full_text.split('\n')[:10]  # Look in first 10 lines
            for line in lines:
                # Common patterns for judge names at the beginning
                if re.search(r"^(?:Justice|J\.|Hon'ble|Judge)\s+", line.strip()):
                    sections["bench"] = line.strip()
                    break
                # Look for "Bench:" at the beginning of the document
                if line.strip().startswith("Bench:"):
                    sections["bench"] = line.strip()[6:].strip()
                    break
        
        # Simple heuristic: look for common section headers
        judgment_markers = ["JUDGMENT", "ORDER", "VERDICT", "DECISION", "IT IS ORDERED"]
        crime_markers = ["ALLEGATIONS", "CHARGES", "CRIME", "OFFENCE", "FACTS OF THE CASE"]
        evidence_markers = ["EVIDENCE", "WITNESS", "TESTIMONY", "EXHIBIT", "PROOF"]
        
        # Split the text into paragraphs
        paragraphs = full_text.split('\n\n')
        
        current_section = "judgement_details"  # Default section
        
        for para in paragraphs:
            para_upper = para.upper()
            
            # Determine which section this paragraph belongs to
            if any(marker in para_upper for marker in judgment_markers):
                current_section = "judgement_details"
            elif any(marker in para_upper for marker in crime_markers):
                current_section = "crime_details"
            elif any(marker in para_upper for marker in evidence_markers):
                current_section = "evidence_details"
                
            # Add paragraph to the current section
            sections[current_section] += para + "\n\n"
        
        # If we couldn't extract meaningful sections, use the full text as judgment details
        if not sections["judgement_details"].strip():
            sections["judgement_details"] = full_text
            
        # Make sure the full text is also preserved
        sections["full_text"] = full_text
            
        return sections


class JudgementAnalyserAgent:
    """Main agent for comprehensive judgement analysis."""
    
    def __init__(self):
        """Initialize the judgement analysis agent."""
        self.memory = MemorySaver()
        self.tools = self._initialize_tools()
        self.llm = LLMProvider.get_llm("groq", "qwen/qwen3-32b")
        self.agent_executor = self._initialize_agent()
        self.verifier = LegalVerifier()
        self.previous_analysis = None  # Store the most recent analysis
        self.metadata = {}  # Store case metadata

        # 🔥 COMPREHENSIVE LEGAL CODE MAPPING SYSTEM
        self._initialize_legal_mappings()

    def _initialize_legal_mappings(self):
        """🔥 Initialize comprehensive mapping between old and new legal codes."""
        logger.info("🔧 Initializing comprehensive IPC↔BNS, CrPC↔BNSS, Evidence Act↔BSA mappings")

        # 🔥 COMPREHENSIVE BNS SECTION MAPPING (IPC → BNS)
        self.bns_section_mapping = {
            # Murder and Culpable Homicide
            "103": {"name": "Murder", "ipc_equivalent": "302", "punishment": "Death or life imprisonment", "cognizable": True, "bailable": False},
            "109": {"name": "Culpable homicide not amounting to murder", "ipc_equivalent": "304", "punishment": "Imprisonment up to 10 years", "cognizable": True, "bailable": False},
            "101": {"name": "Culpable homicide", "ipc_equivalent": "299", "punishment": "As per circumstances", "cognizable": True, "bailable": False},

            # Hurt and Grievous Hurt
            "115": {"name": "Voluntarily causing hurt", "ipc_equivalent": "323", "punishment": "Imprisonment up to 1 year or fine up to ₹1000", "cognizable": False, "bailable": True},
            "117": {"name": "Voluntarily causing grievous hurt", "ipc_equivalent": "322", "punishment": "Imprisonment up to 7 years", "cognizable": True, "bailable": False},
            "118": {"name": "Voluntarily causing grievous hurt by dangerous weapons", "ipc_equivalent": "326", "punishment": "Imprisonment up to 10 years", "cognizable": True, "bailable": False},

            # Theft and Robbery
            "303": {"name": "Theft", "ipc_equivalent": "378", "punishment": "Imprisonment up to 3 years or fine", "cognizable": True, "bailable": True},
            "304": {"name": "Theft in dwelling house", "ipc_equivalent": "380", "punishment": "Imprisonment up to 7 years", "cognizable": True, "bailable": False},
            "309": {"name": "Robbery", "ipc_equivalent": "392", "punishment": "Imprisonment up to 10 years", "cognizable": True, "bailable": False},
            "310": {"name": "Dacoity", "ipc_equivalent": "395", "punishment": "Life imprisonment or imprisonment up to 10 years", "cognizable": True, "bailable": False},

            # Cheating and Fraud
            "318": {"name": "Cheating", "ipc_equivalent": "420", "punishment": "Imprisonment up to 7 years and fine", "cognizable": False, "bailable": True},
            "319": {"name": "Cheating by personation", "ipc_equivalent": "419", "punishment": "Imprisonment up to 3 years or fine", "cognizable": False, "bailable": True},

            # Sexual Offences
            "63": {"name": "Rape", "ipc_equivalent": "375", "punishment": "Imprisonment not less than 10 years, may extend to life", "cognizable": True, "bailable": False},
            "64": {"name": "Punishment for rape", "ipc_equivalent": "376", "punishment": "Imprisonment not less than 10 years", "cognizable": True, "bailable": False},
            "65": {"name": "Punishment for rape in certain cases", "ipc_equivalent": "376A", "punishment": "Death or life imprisonment", "cognizable": True, "bailable": False},

            # Kidnapping and Abduction
            "137": {"name": "Kidnapping", "ipc_equivalent": "359", "punishment": "Imprisonment up to 7 years", "cognizable": True, "bailable": False},
            "140": {"name": "Kidnapping for ransom", "ipc_equivalent": "364A", "punishment": "Death or life imprisonment", "cognizable": True, "bailable": False},

            # Criminal Breach of Trust
            "316": {"name": "Criminal breach of trust", "ipc_equivalent": "405", "punishment": "Imprisonment up to 3 years or fine", "cognizable": False, "bailable": True},
            "317": {"name": "Criminal breach of trust by carrier", "ipc_equivalent": "407", "punishment": "Imprisonment up to 7 years", "cognizable": True, "bailable": False},

            # Mischief
            "324": {"name": "Mischief", "ipc_equivalent": "425", "punishment": "Imprisonment up to 3 months or fine", "cognizable": False, "bailable": True},
            "326": {"name": "Mischief by fire or explosive substance", "ipc_equivalent": "435", "punishment": "Imprisonment up to 7 years", "cognizable": True, "bailable": False},

            # Criminal Intimidation
            "351": {"name": "Criminal intimidation", "ipc_equivalent": "503", "punishment": "Imprisonment up to 2 years or fine", "cognizable": False, "bailable": True},
            "352": {"name": "Criminal intimidation by anonymous communication", "ipc_equivalent": "507", "punishment": "Imprisonment up to 2 years", "cognizable": True, "bailable": True},

            # Defamation
            "356": {"name": "Defamation", "ipc_equivalent": "499", "punishment": "Imprisonment up to 2 years or fine", "cognizable": False, "bailable": True},
            "357": {"name": "Printing or engraving matter known to be defamatory", "ipc_equivalent": "501", "punishment": "Imprisonment up to 2 years or fine", "cognizable": False, "bailable": True},

            # Dowry Death
            "85": {"name": "Dowry death", "ipc_equivalent": "304B", "punishment": "Imprisonment not less than 7 years, may extend to life", "cognizable": True, "bailable": False},

            # Corruption
            "61": {"name": "Criminal conspiracy", "ipc_equivalent": "120B", "punishment": "As per the offence conspired", "cognizable": True, "bailable": False},

            # Attempt to Murder
            "109": {"name": "Attempt to murder", "ipc_equivalent": "307", "punishment": "Imprisonment up to 10 years", "cognizable": True, "bailable": False}
        }

        # 🔥 COMPREHENSIVE BNSS SECTION MAPPING (CrPC → BNSS)
        self.bnss_section_mapping = {
            # FIR and Investigation
            "173": {"name": "Information in cognizable cases", "crpc_equivalent": "154", "description": "FIR registration"},
            "174": {"name": "Information to Magistrate", "crpc_equivalent": "155", "description": "Informing magistrate of cognizable case"},
            "176": {"name": "Investigation by police", "crpc_equivalent": "156", "description": "Police investigation powers"},
            "180": {"name": "Power of police officer to investigate cognizable case", "crpc_equivalent": "157", "description": "Investigation authority"},
            "183": {"name": "Report of investigation", "crpc_equivalent": "173", "description": "Police report submission"},

            # Arrest and Bail
            "35": {"name": "Arrest without warrant", "crpc_equivalent": "41", "description": "Arrest powers without warrant"},
            "36": {"name": "Arrest how made", "crpc_equivalent": "46", "description": "Procedure for arrest"},
            "479": {"name": "When bail may be taken in case of non-bailable offence", "crpc_equivalent": "437", "description": "Bail in non-bailable offences"},
            "480": {"name": "When person to be released on bail", "crpc_equivalent": "436", "description": "Bail in bailable offences"},
            "482": {"name": "Direction for grant of bail to person under the age of sixteen years", "crpc_equivalent": "12", "description": "Juvenile bail provisions"},

            # Search and Seizure
            "93": {"name": "Search of place entered by person sought to be arrested", "crpc_equivalent": "47", "description": "Search during arrest"},
            "96": {"name": "Search warrant", "crpc_equivalent": "93", "description": "Search warrant issuance"},
            "104": {"name": "Power to seize offensive weapons", "crpc_equivalent": "102", "description": "Seizure of weapons"},

            # Cognizance and Trial
            "223": {"name": "Cognizance of offences by Magistrates", "crpc_equivalent": "190", "description": "Taking cognizance"},
            "224": {"name": "Cognizance how taken", "crpc_equivalent": "200", "description": "Procedure for cognizance"},
            "252": {"name": "Framing of charge", "crpc_equivalent": "228", "description": "Charge framing procedure"},
            "265": {"name": "Evidence for prosecution", "crpc_equivalent": "243", "description": "Prosecution evidence"},
            "284": {"name": "Judgment", "crpc_equivalent": "353", "description": "Judgment delivery"},

            # Appeals and Revision
            "383": {"name": "Appeal from orders of acquittal", "crpc_equivalent": "378", "description": "Appeal against acquittal"},
            "384": {"name": "Appeal from conviction", "crpc_equivalent": "374", "description": "Appeal against conviction"},
            "419": {"name": "Power to call for records", "crpc_equivalent": "397", "description": "Revision powers"},

            # Maintenance and Welfare
            "144": {"name": "Order for maintenance of wives, children and parents", "crpc_equivalent": "125", "description": "Maintenance orders"},

            # Preventive Action
            "126": {"name": "Power to require security for good behaviour", "crpc_equivalent": "106", "description": "Security for good behaviour"},
            "127": {"name": "Power to require security for keeping the peace", "crpc_equivalent": "107", "description": "Security for peace"},

            # Summons and Warrants
            "63": {"name": "Summons how served", "crpc_equivalent": "62", "description": "Service of summons"},
            "70": {"name": "Warrant of arrest", "crpc_equivalent": "70", "description": "Arrest warrant"},
            "78": {"name": "Warrant to whom directed", "crpc_equivalent": "72", "description": "Direction of warrant"}
        }

        # 🔥 COMPREHENSIVE BSA SECTION MAPPING (Evidence Act → BSA)
        self.bsa_section_mapping = {
            # Basic Definitions
            "3": {"name": "Facts", "evidence_act_equivalent": "3", "description": "Definition of facts"},
            "5": {"name": "Evidence", "evidence_act_equivalent": "3", "description": "Definition of evidence"},
            "6": {"name": "Relevancy of facts", "evidence_act_equivalent": "5", "description": "Relevant facts"},

            # Admissibility
            "22": {"name": "Oral evidence must be direct", "evidence_act_equivalent": "60", "description": "Direct oral evidence"},
            "23": {"name": "Oral evidence of contents of documents", "evidence_act_equivalent": "61", "description": "Oral evidence of documents"},
            "24": {"name": "Cases in which statement of relevant fact by person who is dead or cannot be found is relevant", "evidence_act_equivalent": "32", "description": "Dying declarations and statements"},

            # Documentary Evidence
            "61": {"name": "Proof of contents of documents", "evidence_act_equivalent": "61", "description": "Documentary proof"},
            "62": {"name": "Primary evidence", "evidence_act_equivalent": "62", "description": "Primary evidence definition"},
            "63": {"name": "Admissibility of electronic records", "evidence_act_equivalent": "65A", "description": "Electronic evidence admissibility"},
            "64": {"name": "Special provisions as to evidence relating to electronic record", "evidence_act_equivalent": "65B", "description": "Electronic records evidence"},
            "65": {"name": "Presumption as to electronic records", "evidence_act_equivalent": "88A", "description": "Electronic records presumption"},

            # Expert Evidence
            "45": {"name": "Opinions of experts", "evidence_act_equivalent": "45", "description": "Expert testimony"},
            "46": {"name": "Facts bearing upon opinions of experts", "evidence_act_equivalent": "46", "description": "Facts supporting expert opinion"},
            "47": {"name": "Opinion as to handwriting", "evidence_act_equivalent": "47", "description": "Handwriting expert opinion"},
            "48": {"name": "Opinion as to digital signature", "evidence_act_equivalent": "47A", "description": "Digital signature verification"},

            # Character Evidence
            "52": {"name": "In civil cases character to prove conduct imputed irrelevant", "evidence_act_equivalent": "52", "description": "Character evidence in civil cases"},
            "53": {"name": "In criminal cases previous good character relevant", "evidence_act_equivalent": "53", "description": "Good character in criminal cases"},
            "54": {"name": "Previous bad character not relevant except in reply", "evidence_act_equivalent": "54", "description": "Bad character evidence"},

            # Confessions and Admissions
            "19": {"name": "Admission defined", "evidence_act_equivalent": "17", "description": "Definition of admission"},
            "20": {"name": "Admission by party to proceeding or his agent", "evidence_act_equivalent": "18", "description": "Party admissions"},
            "24": {"name": "Confession caused by inducement, threat or promise irrelevant in criminal proceeding", "evidence_act_equivalent": "24", "description": "Involuntary confessions"},
            "25": {"name": "Confession to police officer not to be proved", "evidence_act_equivalent": "25", "description": "Police confessions inadmissible"},
            "26": {"name": "Confession by accused while in custody of police not to be proved against him", "evidence_act_equivalent": "26", "description": "Custodial confessions"},
            "27": {"name": "How much of information received from accused may be proved", "evidence_act_equivalent": "27", "description": "Information leading to discovery"},

            # Presumptions
            "79": {"name": "Presumption as to genuineness of certified copies", "evidence_act_equivalent": "79", "description": "Certified copies presumption"},
            "81": {"name": "Presumption as to Gazette", "evidence_act_equivalent": "81", "description": "Official Gazette presumption"},
            "82": {"name": "Presumption as to document produced as record of evidence", "evidence_act_equivalent": "80", "description": "Court records presumption"},
            "85": {"name": "Presumption as to powers of attorney", "evidence_act_equivalent": "85", "description": "Power of attorney presumption"},
            "87": {"name": "Presumption as to telegraphic messages", "evidence_act_equivalent": "87", "description": "Telegraph messages presumption"},
            "88": {"name": "Presumption as to due execution", "evidence_act_equivalent": "68", "description": "Due execution presumption"},

            # Burden of Proof
            "96": {"name": "Burden of proof", "evidence_act_equivalent": "101", "description": "General burden of proof"},
            "97": {"name": "On whom burden of proof lies", "evidence_act_equivalent": "102", "description": "Burden allocation"},
            "98": {"name": "Burden of proof as to particular fact", "evidence_act_equivalent": "103", "description": "Specific fact burden"},
            "99": {"name": "Burden of proving fact to be proved to make evidence admissible", "evidence_act_equivalent": "104", "description": "Admissibility burden"},
            "100": {"name": "Burden of proving that case of accused comes within exceptions", "evidence_act_equivalent": "105", "description": "Exception burden"},

            # Examination of Witnesses
            "135": {"name": "Order of production and examination of witnesses", "evidence_act_equivalent": "134", "description": "Witness examination order"},
            "137": {"name": "Cross-examination of person called to produce a document", "evidence_act_equivalent": "136", "description": "Document producer cross-examination"},
            "138": {"name": "Re-examination", "evidence_act_equivalent": "137", "description": "Re-examination procedure"},
            "139": {"name": "Direction of Court as to questions", "evidence_act_equivalent": "138", "description": "Court direction on questions"},
            "146": {"name": "Questions lawful in cross-examination", "evidence_act_equivalent": "146", "description": "Lawful cross-examination"},
            "147": {"name": "When witness may be compelled to answer", "evidence_act_equivalent": "147", "description": "Compulsory answers"},
            "148": {"name": "Court to decide when question shall be asked and when witness compelled to answer", "evidence_act_equivalent": "148", "description": "Court discretion on questions"},
            "149": {"name": "Question not to be asked without reasonable grounds", "evidence_act_equivalent": "149", "description": "Reasonable grounds requirement"},
            "150": {"name": "Procedure of Court in case of question being asked without reasonable grounds", "evidence_act_equivalent": "150", "description": "Court procedure for improper questions"},
            "151": {"name": "Indecent and scandalous questions", "evidence_act_equivalent": "151", "description": "Prohibition of indecent questions"},
            "152": {"name": "Questions intended to insult or annoy", "evidence_act_equivalent": "152", "description": "Prohibition of insulting questions"},
            "153": {"name": "Exclusion of evidence to contradict answers to questions testing veracity", "evidence_act_equivalent": "153", "description": "Collateral matter rule"},
            "154": {"name": "Question by party to his own witness", "evidence_act_equivalent": "154", "description": "Leading questions prohibition"},
            "155": {"name": "Impeaching credit of witness", "evidence_act_equivalent": "155", "description": "Witness credibility impeachment"}
        }

        logger.info(f"✅ Legal mappings initialized: {len(self.bns_section_mapping)} BNS sections, {len(self.bnss_section_mapping)} BNSS sections, {len(self.bsa_section_mapping)} BSA sections")

    def get_section_mapping(self, code: str, section: str) -> Dict[str, Any]:
        """
        🔥 COMPREHENSIVE: Get mapping between old and new legal codes.

        Args:
            code: Legal code (BNS, BNSS, BSA, IPC, CrPC, Evidence Act)
            section: Section number

        Returns:
            Dictionary with mapping details including old/new equivalents
        """
        # Forward mapping for BNS new code with IPC legacy
        if code == "BNS" and section in self.bns_section_mapping:
            details = self.bns_section_mapping[section].copy()
            return {
                "new_code": "BNS",
                "new_section": section,
                "legacy_code": "IPC",
                "legacy_section": details.get("ipc_equivalent"),
                **details
            }

        # Reverse mapping from legacy IPC to new BNS
        if code == "IPC":
            for new_sec, val in self.bns_section_mapping.items():
                if val.get("ipc_equivalent") == section:
                    details = val.copy()
                    return {
                        "new_code": "BNS",
                        "new_section": new_sec,
                        "legacy_code": "IPC",
                        "legacy_section": section,
                        **details
                    }

        # Forward mapping for BNSS new code with CrPC legacy
        if code == "BNSS" and section in self.bnss_section_mapping:
            details = self.bnss_section_mapping[section].copy()
            return {
                "new_code": "BNSS",
                "new_section": section,
                "legacy_code": "CrPC",
                "legacy_section": details.get("crpc_equivalent"),
                **details
            }

        # Reverse mapping from legacy CrPC to new BNSS
        if code == "CrPC":
            for new_sec, val in self.bnss_section_mapping.items():
                if val.get("crpc_equivalent") == section:
                    details = val.copy()
                    return {
                        "new_code": "BNSS",
                        "new_section": new_sec,
                        "legacy_code": "CrPC",
                        "legacy_section": section,
                        **details
                    }

        # Forward mapping for BSA new code with Evidence Act legacy
        if code == "BSA" and section in self.bsa_section_mapping:
            details = self.bsa_section_mapping[section].copy()
            return {
                "new_code": "BSA",
                "new_section": section,
                "legacy_code": "Evidence Act",
                "legacy_section": details.get("evidence_act_equivalent"),
                **details
            }

        # Reverse mapping from legacy Evidence Act to new BSA
        if code == "Evidence Act":
            for new_sec, val in self.bsa_section_mapping.items():
                if val.get("evidence_act_equivalent") == section:
                    details = val.copy()
                    return {
                        "new_code": "BSA",
                        "new_section": new_sec,
                        "legacy_code": "Evidence Act",
                        "legacy_section": section,
                        **details
                    }

        # Fallback for unknown sections
        return {
            "name": "Unknown section",
            "description": "Section details not available in current mapping",
            "code": code,
            "section": section,
            "note": "This section may need manual verification"
        }

    def format_section_with_mapping(self, code: str, section: str) -> str:
        """
        🔥 FORMAT: Format section with proper old/new code mapping.

        Args:
            code: Legal code
            section: Section number

        Returns:
            Formatted string with mapping (e.g., "BNS Section 103 (formerly IPC Section 302)")
        """
        mapping = self.get_section_mapping(code, section)

        if mapping.get("new_code") and mapping.get("legacy_code"):
            if code in ["BNS", "BNSS", "BSA"]:  # New codes
                return f"{mapping['new_code']} Section {mapping['new_section']} (formerly {mapping['legacy_code']} Section {mapping['legacy_section']})"
            else:  # Old codes
                return f"{mapping['new_code']} Section {mapping['new_section']} (formerly {mapping['legacy_code']} Section {mapping['legacy_section']})"
        else:
            return f"{code} Section {section}"

    def get_all_related_sections(self, text: str) -> Dict[str, List[Dict]]:
        """
        🔥 EXTRACT: Extract all legal sections from text and provide mappings.

        Args:
            text: Text to analyze

        Returns:
            Dictionary with categorized sections and their mappings
        """
        sections_found = {
            "bns_sections": [],
            "bnss_sections": [],
            "bsa_sections": [],
            "ipc_sections": [],
            "crpc_sections": [],
            "evidence_act_sections": [],
            "unmapped_sections": []
        }

        # Enhanced patterns to extract sections with their codes
        patterns = [
            (r'BNS\s+Section\s+(\d+[A-Z]*)', "BNS"),
            (r'BNSS\s+Section\s+(\d+[A-Z]*)', "BNSS"),
            (r'BSA\s+Section\s+(\d+[A-Z]*)', "BSA"),
            (r'IPC\s+Section\s+(\d+[A-Z]*)', "IPC"),
            (r'CrPC\s+Section\s+(\d+[A-Z]*)', "CrPC"),
            (r'Evidence\s+Act\s+Section\s+(\d+[A-Z]*)', "Evidence Act"),
            (r'Section\s+(\d+[A-Z]*)\s+of\s+(?:the\s+)?BNS', "BNS"),
            (r'Section\s+(\d+[A-Z]*)\s+of\s+(?:the\s+)?BNSS', "BNSS"),
            (r'Section\s+(\d+[A-Z]*)\s+of\s+(?:the\s+)?BSA', "BSA"),
            (r'Section\s+(\d+[A-Z]*)\s+of\s+(?:the\s+)?IPC', "IPC"),
            (r'Section\s+(\d+[A-Z]*)\s+of\s+(?:the\s+)?CrPC', "CrPC"),
            (r'Section\s+(\d+[A-Z]*)\s+of\s+(?:the\s+)?Evidence\s+Act', "Evidence Act"),
        ]

        for pattern, code in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for section in matches:
                mapping = self.get_section_mapping(code, section)
                section_info = {
                    "original_code": code,
                    "original_section": section,
                    "mapping": mapping,
                    "formatted": self.format_section_with_mapping(code, section)
                }

                # Categorize based on original code
                if code == "BNS":
                    sections_found["bns_sections"].append(section_info)
                elif code == "BNSS":
                    sections_found["bnss_sections"].append(section_info)
                elif code == "BSA":
                    sections_found["bsa_sections"].append(section_info)
                elif code == "IPC":
                    sections_found["ipc_sections"].append(section_info)
                elif code == "CrPC":
                    sections_found["crpc_sections"].append(section_info)
                elif code == "Evidence Act":
                    sections_found["evidence_act_sections"].append(section_info)
                else:
                    sections_found["unmapped_sections"].append(section_info)

        return sections_found

    def _initialize_tools(self) -> List[BaseTool]:
        """Initialize and return the premium search and analysis tools."""
        # Get premium judgement analysis search tools (no DuckDuckGo for stability)
        tools = EnhancedJudgementAnalysisSearchFactory.get_premium_judgement_analysis_tools()
        return tools
    
    def _initialize_agent(self) -> Runnable:
        """Initialize and return the agent executor."""
        try:
            logger.info("Creating ReAct agent with tools")
            # Create a ReAct agent
            return create_react_agent(
                self.llm,
                self.tools,
                checkpointer=self.memory
            )
        except Exception as e:
            logger.error(f"Error creating ReAct agent: {e}")
            logger.info("Creating simplified agent without tools")
            
            # Fallback: Create a simpler agent without tools if the ReAct agent fails
            # This just wraps the LLM to maintain API compatibility
            def simple_agent_invoke(inputs, config=None):
                messages = inputs.get("messages", [])
                response = self.llm.invoke(messages)
                return {"messages": [response]}
            
            simple_agent = lambda: None  # Create a dummy object
            simple_agent.invoke = simple_agent_invoke
            simple_agent.stream = lambda inputs, config=None: [simple_agent_invoke(inputs, config)]
            
            return simple_agent
    
    def _construct_comprehensive_advice_prompt(self, query: JudgementQuery) -> str:
        """
        Construct a comprehensive judgement advice prompt covering all perspectives and outcomes.
        """
        prompt = f"""You are a distinguished Chief Justice of the Supreme Court of India with 30+ years of experience. Provide a comprehensive analysis of this judgement from every possible angle.

IMPORTANT: Always respond in ENGLISH regardless of the language of the input.

CRITICAL LEGAL FRAMEWORK UPDATE (July 2024):
- Use BHARATIYA NYAYA SANHITA (BNS) as PRIMARY reference (replaced IPC)
- Use BHARATIYA NAGARIK SURAKSHA SANHITA (BNSS) as PRIMARY reference (replaced CrPC) 
- Use BHARATIYA SAKSHYA ADHINIYAM (BSA) as PRIMARY reference (replaced Evidence Act)
- Always provide OLD CODE MAPPING for reference: BNS Section X (formerly IPC Section Y)

JUDGEMENT DETAILS:
{query.judgement_details}

CRIME DETAILS:
{query.crime_details}

EVIDENCE DETAILS:
{query.evidence_details}
"""
        
        if hasattr(query, 'metadata') and query.metadata:
            prompt += f"\nCASE METADATA:\n"
            for key, value in query.metadata.items():
                if value:
                    prompt += f"- {key.replace('_', ' ').title()}: {value}\n"
        
        prompt += """
Your comprehensive analysis MUST include:

1. EXECUTIVE SUMMARY
   - Brief overview of the case and judgement
   - Key legal issues involved under NEW LEGAL FRAMEWORK (BNS/BNSS/BSA)
   - Primary outcome and its significance

2. JUDGEMENT VALIDITY ASSESSMENT
   - Detailed analysis using PRIMARY REFERENCES: BNS, BNSS, BSA
   - Cross-reference with old codes: IPC, CrPC, Evidence Act (for historical context)
   - Format: "BNS Section 103 (formerly IPC Section 302)" for murder
   - Format: "BNSS Section 154 (formerly CrPC Section 154)" for FIR
   - Format: "BSA Section 45 (formerly Evidence Act Section 45)" for expert testimony
   - Procedural adherence and due process evaluation under BNSS
   - Evidence-judgement alignment assessment under BSA

3. MULTI-PERSPECTIVE ANALYSIS
   a) FOR THE ACCUSED/DEFENDANT:
      - Strengths and weaknesses under BNS/BNSS/BSA framework
      - Potential grounds for appeal with NEW CODE sections (with old code mapping)
      - Risk assessment and mitigation strategies under BNSS procedures
      - Timeline and procedural options under BNSS
   
   b) FOR THE PROSECUTION/PETITIONER:
      - Case strengths under BNS definitions and BSA evidence rules
      - Methods to defend against appeals under BNSS procedures
      - Additional evidence collection under BSA provisions
      - Enforcement strategies under new legal framework
   
   c) FOR SOCIETY/PUBLIC INTEREST:
      - Broader implications under the new criminal justice system
      - Precedent value under BNS/BNSS/BSA framework
      - Social justice aspects under updated legal provisions

4. LEGAL COMPLIANCE WITH NEW CODES
   - Primary Analysis: BNS Sections (with IPC equivalents in brackets)
   - Procedural Compliance: BNSS Sections (with CrPC equivalents in brackets)  
   - Evidence Evaluation: BSA Sections (with Evidence Act equivalents in brackets)
   - Example Format: "Under BNS Section 103 (IPC Section 302), the elements of murder are..."

5. PROS AND CONS ANALYSIS
   - Strengths of judgement under new legal framework
   - Potential issues with BNS/BNSS/BSA compliance
   - How judgement aligns with updated criminal justice objectives
   - Areas where new code provisions could strengthen/weaken the case

6. ALL POSSIBLE OUTCOMES & SCENARIOS
   - Implications under the new legal framework
   - Appeal prospects under BNSS procedures
   - Long-term impact under BNS/BNSS/BSA system
   - Enforcement challenges/opportunities under new codes

7. LANDMARK CASES & LEGAL PRECEDENTS
   - 5-7 most relevant Supreme Court/High Court cases
   - How precedents apply to new BNS/BNSS/BSA framework
   - Transitional jurisprudence between old and new codes
   - Evolving interpretations under new legal system

8. PRACTICAL GUIDANCE & NEXT STEPS
   - Immediate actions under BNSS procedures
   - Timeline for various legal options under new framework
   - Cost-benefit analysis considering new code provisions
   - Strategic recommendations under BNS/BNSS/BSA system

9. COMPREHENSIVE LEGAL REFERENCES & MAPPING
   - PRIMARY: All applicable BNS sections with detailed explanations
   - SECONDARY: Corresponding IPC sections (for reference only)
   - PRIMARY: All applicable BNSS sections with detailed explanations  
   - SECONDARY: Corresponding CrPC sections (for reference only)
   - PRIMARY: All applicable BSA sections with detailed explanations
   - SECONDARY: Corresponding Evidence Act sections (for reference only)
   - PMLA sections (if applicable)
   - Supporting case law with citations

MANDATORY FORMAT FOR LEGAL REFERENCES:
- "BNS Section 103 (formerly IPC Section 302) - Murder"
- "BNSS Section 154 (formerly CrPC Section 154) - First Information Report"
- "BSA Section 45 (formerly Evidence Act Section 45) - Expert Testimony"

RESPOND IN ENGLISH ONLY. Use the NEW LEGAL CODES as primary reference with old code mapping for comprehensive understanding.

Format your response with clear headings and subheadings for easy navigation.
"""
        return prompt

    def analyze_judgement(self, query: Union[str, JudgementQuery], config: Optional[Dict[str, Any]] = None, metadata: Optional[Dict[str, Any]] = None, include_detailed_analysis: bool = False) -> Dict[str, Any]:
        """
        Analyze a legal judgement and provide comprehensive assessment.
        
        Args:
            query: A string description of the judgement or a JudgementQuery object
            config: Optional configuration for the agent execution
            metadata: Optional metadata about the case (case number, bench, etc.)
            include_detailed_analysis: Whether to include detailed round-by-round analysis
            
        Returns:
            A dictionary with the agent's response
        """
        if config is None:
            config = {"configurable": {"thread_id": "judgement-analysis-001"}}
            
        # Store metadata if provided
        if metadata:
            self.metadata = metadata
            
        # Convert string query to JudgementQuery if needed
        if isinstance(query, str):
            query = JudgementQuery(judgement_details=query)
            
        # If query contains metadata fields, extract them
        if hasattr(query, 'metadata') and query.metadata:
            self.metadata.update(query.metadata)

        # Step 1: Generate comprehensive advice first
        advice_prompt = self._construct_comprehensive_advice_prompt(query)
        advice_system_message = """You are a distinguished Chief Justice of the Supreme Court of India with 30+ years of experience. Provide comprehensive, practical, and well-referenced judgement analysis. Always use Google search for references and case law. RESPOND IN ENGLISH ONLY regardless of the input language."""
        
        try:
            advice_response = self.llm.invoke([
                SystemMessage(content=advice_system_message),
                HumanMessage(content=advice_prompt)
            ])
            advice_content = advice_response.content if hasattr(advice_response, 'content') else str(advice_response)
            advice_verified = self.verifier.verify_analysis(advice_content)
        except Exception as e:
            advice_verified = f"Comprehensive analysis generation failed: {e}"

        # Step 2: Add note about detailed analysis
        advice_verified += "\n\n---\n\n**Note:** This is a comprehensive overview. For detailed section-by-section analysis with specific legal arguments, set `include_detailed_analysis=True`."

        # Step 3: Generate detailed analysis if requested
        if include_detailed_analysis:
            detailed_prompt = self._construct_judgement_prompt(query)
            detailed_system_message = """You are a Chief Justice of the Supreme Court of India with extensive experience in reviewing and analyzing judicial decisions. Your analysis must be impartial, thorough, and backed by relevant legal codes including BNS, BNSS, BSA, IPC, CrPC, and PMLA. RESPOND IN ENGLISH ONLY regardless of the input language."""
            
            try:
                response = None
                try:
                    response = self.agent_executor.invoke(
                        {"messages": [
                            SystemMessage(content=detailed_system_message),
                            HumanMessage(content=detailed_prompt)
                        ]},
                        config
                    )
                except Exception as agent_error:
                    llm_response = self.llm.invoke([
                        SystemMessage(content=detailed_system_message),
                        HumanMessage(content=detailed_prompt)
                    ])
                    response = {"messages": [llm_response]}
                
                # Extract content from response
                content = None
                if isinstance(response, dict) and "agent" in response and "messages" in response["agent"]:
                    messages = response["agent"]["messages"]
                    if messages and len(messages) > 0:
                        message = messages[-1]
                        if hasattr(message, 'content'):
                            content = message.content
                
                if content is None and isinstance(response, dict) and "messages" in response:
                    messages = response["messages"]
                    if messages and len(messages) > 0:
                        message = messages[-1]
                        if hasattr(message, 'content'):
                            content = message.content
                
                if content is None and hasattr(response, 'content'):
                    content = response.content
                
                if content:
                    verified_content = self.verifier.verify_analysis(content)
                    self.previous_analysis = advice_verified + "\n\n---\n\n## DETAILED LEGAL ANALYSIS\n\n" + verified_content
                    
                    # Return the combined analysis
                    return {"messages": [AIMessage(content=self.previous_analysis)]}
                else:
                    return {"messages": [AIMessage(content=advice_verified + "\n\n---\n\nDetailed analysis generation failed.")]}
                    
            except Exception as e:
                return {"messages": [AIMessage(content=advice_verified + f"\n\n---\n\nDetailed analysis generation failed: {e}")]}
        
        # Store the analysis for future questions
        self.previous_analysis = advice_verified
        return {"messages": [AIMessage(content=advice_verified)]}

    def stream_analysis(self, query: Union[str, JudgementQuery], config: Optional[Dict[str, Any]] = None):
        """
        Stream the analysis results as they are generated.
        
        Args:
            query: A string description of the judgment or a JudgementQuery object
            config: Optional configuration for the agent execution
            
        Returns:
            A generator yielding chunks of the agent's response
        """
        if config is None:
            config = {"configurable": {"thread_id": "judgement-analysis-001"}}
            
        # Convert string query to JudgementQuery if needed
        if isinstance(query, str):
            query = JudgementQuery(judgement_details=query)
            
        # Construct the prompt
        human_prompt = self._construct_judgement_prompt(query)
        system_prompt = """You are a Chief Justice of the Supreme Court of India with extensive experience in reviewing and analyzing judicial decisions. Your analysis must be impartial, thorough, and backed by relevant legal codes including BNS, BNSS, BSA, IPC, CrPC, and PMLA."""
        
        # Stream chunks through the agent executor
        logger.info("Starting streamed judgement analysis")
        chunks = []
        
        try:
            # First try to stream with the agent executor
            for chunk in self.agent_executor.stream(
                {"messages": [
                    SystemMessage(content=system_prompt),
                    HumanMessage(content=human_prompt)
                ]},
                config
            ):
                chunks.append(chunk)
                yield chunk  # Yield each chunk as it comes in
                
            logger.info(f"Streaming completed with {len(chunks)} chunks")
                
        except Exception as stream_error:
            logger.warning(f"Agent streaming failed: {str(stream_error)}")
            logger.info("Falling back to direct LLM call")
            
            # Fallback to direct LLM call (won't be streamed)
            try:
                llm_response = self.llm.invoke([
                    SystemMessage(content=system_prompt),
                    HumanMessage(content=human_prompt)
                ])
                
                # Create a chunk mimicking the agent executor format
                fallback_chunk = {"messages": [llm_response]}
                chunks.append(fallback_chunk)
                yield fallback_chunk
                
            except Exception as llm_error:
                logger.error(f"Direct LLM call also failed: {str(llm_error)}")
                error_chunk = {"error": f"Both streaming and direct LLM failed: {str(llm_error)}"}
                yield error_chunk
                return
        
        # If we have at least one chunk, verify the last one before returning it
        if chunks:
            last_chunk = chunks[-1]
            content = None
            
            # Try to extract content from the last chunk
            if isinstance(last_chunk, dict):
                # Try agent.messages structure
                if "agent" in last_chunk and "messages" in last_chunk["agent"]:
                    messages = last_chunk["agent"]["messages"]
                    if messages and len(messages) > 0:
                        message = messages[-1]
                        if hasattr(message, 'content'):
                            content = message.content
                
                # Try direct messages structure
                elif "messages" in last_chunk:
                    messages = last_chunk["messages"]
                    if messages and len(messages) > 0:
                        message = messages[-1]
                        if hasattr(message, 'content'):
                            content = message.content
            
            # If we found content, verify it and yield a final verified chunk
            if content:
                logger.info("Verifying legal references in final streamed response")
                verified_content = self.verifier.verify_analysis(content)
                
                # Create a new verified chunk (follow the same format as the last chunk)
                verified_chunk = dict(last_chunk)  # Copy the structure
                
                # Update the content in the appropriate place
                if "agent" in verified_chunk and "messages" in verified_chunk["agent"]:
                    verified_chunk["agent"]["messages"][-1] = AIMessage(content=verified_content)
                elif "messages" in verified_chunk:
                    verified_chunk["messages"][-1] = AIMessage(content=verified_content)
                
                # Yield the final verified chunk
                yield verified_chunk
                logger.info("Yielded final verified chunk")
            
    def _construct_judgement_prompt(self, query: JudgementQuery) -> str:
        """
        Construct a detailed judgement analysis prompt based on the query.

        Args:
            query: The JudgementQuery object

        Returns:
            A formatted prompt string
        """
        # 🔥 ENHANCED: Extract section mappings from the judgment text
        combined_text = f"{query.judgement_details} {query.crime_details or ''} {query.evidence_details or ''}"
        section_mappings = self.get_all_related_sections(combined_text)

        # 🔥 ENHANCED: Create section mapping summary for the prompt
        mapping_summary = ""
        if section_mappings:
            mapping_summary = "\n## 🔥 DETECTED LEGAL SECTIONS WITH MAPPINGS:\n"

            if section_mappings["bns_sections"]:
                mapping_summary += "**BNS Sections Found:**\n"
                for section in section_mappings["bns_sections"][:5]:  # Limit to first 5
                    mapping_summary += f"- {section['formatted']}: {section['mapping'].get('name', 'N/A')}\n"

            if section_mappings["bnss_sections"]:
                mapping_summary += "**BNSS Sections Found:**\n"
                for section in section_mappings["bnss_sections"][:5]:
                    mapping_summary += f"- {section['formatted']}: {section['mapping'].get('name', 'N/A')}\n"

            if section_mappings["bsa_sections"]:
                mapping_summary += "**BSA Sections Found:**\n"
                for section in section_mappings["bsa_sections"][:5]:
                    mapping_summary += f"- {section['formatted']}: {section['mapping'].get('name', 'N/A')}\n"

            if section_mappings["ipc_sections"]:
                mapping_summary += "**IPC Sections Found (map to BNS):**\n"
                for section in section_mappings["ipc_sections"][:5]:
                    mapping_summary += f"- {section['formatted']}: {section['mapping'].get('name', 'N/A')}\n"

            if section_mappings["crpc_sections"]:
                mapping_summary += "**CrPC Sections Found (map to BNSS):**\n"
                for section in section_mappings["crpc_sections"][:5]:
                    mapping_summary += f"- {section['formatted']}: {section['mapping'].get('name', 'N/A')}\n"

            if section_mappings["evidence_act_sections"]:
                mapping_summary += "**Evidence Act Sections Found (map to BSA):**\n"
                for section in section_mappings["evidence_act_sections"][:5]:
                    mapping_summary += f"- {section['formatted']}: {section['mapping'].get('name', 'N/A')}\n"

        prompt = f"""
You are a highly experienced legal expert specializing in the Indian Judiciary System, with deep expertise in:
- Bharatiya Nyaya Sanhita (BNS) which replaced the Indian Penal Code (IPC) in July 2024
- Bharatiya Nagarik Suraksha Sanhita (BNSS) which replaced the Criminal Procedure Code (CrPC)
- Bharatiya Sakshya Adhiniyam (BSA) which replaced the Indian Evidence Act
- Prevention of Money Laundering Act (PMLA)
- Supreme Court and High Court precedents

IMPORTANT: Always respond in ENGLISH regardless of the language of the input.

CRITICAL: Use NEW LEGAL CODES as PRIMARY reference with OLD CODE MAPPING:
- BNS Section X (formerly IPC Section Y)
- BNSS Section X (formerly CrPC Section Y)
- BSA Section X (formerly Evidence Act Section Y)

TASK: Provide a detailed, section-by-section analysis of the provided court judgment.
{mapping_summary}

## Input Data:
- Judgment Details: {query.judgement_details}
- Crime Details: {query.crime_details}
- Evidence Details: {query.evidence_details}

## Detailed Analysis Requirements:

1. JUDGMENT VALIDITY ASSESSMENT (40% of analysis):
   - PRIMARY ANALYSIS: Determine if the judgment correctly applies relevant sections of BNS, BNSS, BSA, and PMLA
   - CROSS-REFERENCE: Map to corresponding IPC, CrPC, Evidence Act sections for historical context
   - MANDATORY FORMAT: "BNS Section 103 (formerly IPC Section 302) - Murder"
   - Assess if the judgment follows binding precedents under new legal framework
   - Evaluate procedural compliance with BNSS (formerly CrPC) due process
   - Consider whether evidence supports judgment under BSA (formerly Evidence Act)
   - Analyze if punishment is proportionate under BNS (formerly IPC) provisions

2. DETAILED RATING (if judgment is valid):
   - Score the judgment on a scale of 1-10 based on BNS/BNSS/BSA compliance
   - Justify rating with specific NEW CODE reasoning and old code comparisons
   - Highlight particularly strong aspects under new legal framework

3. ALTERNATIVE JUDGMENT PROPOSAL (if judgment is flawed):
   - Provide alternative using PRIMARY BNS/BNSS/BSA sections
   - Include old code equivalents for reference: "BNS Section X (formerly IPC Section Y)"
   - Explain why proposed judgment better aligns with new legal framework
   - Reference relevant case law supporting alternative under new codes

4. GUIDANCE FOR PARTIES (40% of analysis):
   a) For the ACCUSED:
      - Strategic appeals grounds with NEW CODE sections (with old code mapping)
      - Procedural errors under BNSS (formerly CrPC) that could be challenged
      - Evidence weaknesses under BSA (formerly Evidence Act) to highlight
      - Precedents supporting accused under BNS/BNSS/BSA framework
      - Timeline and jurisdiction for appeals under BNSS procedures
   
   b) For the PETITIONER/PROSECUTION:
      - Case strengthening methods under BNS definitions and BSA evidence rules
      - Additional evidence gathering under BSA (formerly Evidence Act) provisions
      - Legal arguments to counter appeals under BNSS (formerly CrPC) procedures
      - Procedural steps for judgment execution under new framework
      - Strategies addressing judgment weaknesses under BNS/BNSS/BSA

5. LANDMARK CASES & PRECEDENTS (20% of analysis):
   - Cite 5-7 landmark judgments and their relevance to BNS/BNSS/BSA framework
   - Explain how precedents apply under new legal codes vs old codes
   - Note transitional jurisprudence between IPC→BNS, CrPC→BNSS, Evidence Act→BSA
   - Evolving legal interpretations under new criminal justice system

RESPOND IN ENGLISH ONLY.

MANDATORY SECTION REFERENCE FORMAT:
- "BNS Section 103 (formerly IPC Section 302) - Murder"  
- "BNSS Section 154 (formerly CrPC Section 154) - First Information Report"
- "BSA Section 45 (formerly Evidence Act Section 45) - Expert Testimony"

Format your analysis with clear headings for each section. Provide comprehensive analysis with specific BNS/BNSS/BSA section numbers as PRIMARY references, with IPC/CrPC/Evidence Act equivalents in brackets for historical context.

Your assessment must be impartial, evidence-based, and focused on legal reasoning under the NEW LEGAL FRAMEWORK. Limit your analysis to 2500 words maximum.
"""
        return prompt

    def answer_follow_up_question(self, question: str, previous_analysis: str = None, original_input: str = None) -> str:
        """
        Answer a follow-up question about a previously analyzed judgement.
        
        Args:
            question: The follow-up question about the judgement or analysis
            previous_analysis: The previous analysis content (optional)
            original_input: The original judgement text/content (optional)
            
        Returns:
            Answer to the follow-up question
        """
        logger.info(f"Processing follow-up question: {question[:50]}...")
        
        # Use provided analysis or the one in memory
        analysis_content = previous_analysis or self.previous_analysis
        
        if not analysis_content:
            return "I don't have any previous analysis to reference. Please analyze a judgement first."
        
        # Initialize search tools if needed
        search_tools = []
        search_results = []
        
        try:
            # Try to initialize Google Search directly 
            search_query = self._construct_legal_search_query(question, original_input)
            logger.info(f"Constructed search query: {search_query}")
            
            # Try direct Google search first
            try:
                google_search = GoogleSearchAPIWrapper(
                    google_api_key=GOOGLE_API_KEY,
                    google_cse_id=GOOGLE_CSE_ID
                )
                search_results = google_search.run(search_query)
                logger.info("Successfully executed Google search")
                
                # Format results if they're a string
                if isinstance(search_results, str):
                    # Extract URLs and titles using regex
                    urls = re.findall(r'https?://[^\s]+', search_results)
                    formatted_results = []
                    
                    # Get title snippets - assuming format with newlines or periods
                    lines = search_results.split('\n')
                    
                    for i, url in enumerate(urls[:3]):  # Limit to top 3
                        title = f"Result {i+1}"
                        # Try to get a title from nearby text
                        if i < len(lines):
                            title = lines[i].strip()
                        
                        formatted_results.append({
                            "title": title,
                            "link": url,
                            "snippet": f"Found via Google Search for '{search_query}'"
                        })
                    
                    search_results = formatted_results
            except Exception as e:
                logger.warning(f"Direct Google search failed: {e}")
                
                # Fall back to tool-based approach
                google_search_tool = EnhancedJudgementAnalysisSearchFactory.create_enhanced_google_search()
                if google_search_tool:
                    search_tools.append(google_search_tool)
                
                # Try Tavily Search as backup
                tavily_search = EnhancedJudgementAnalysisSearchFactory.create_enhanced_tavily_search()
                if tavily_search:
                    search_tools.append(tavily_search)
                
                # If we got no search results yet but have tools, try the tools
                if not search_results and search_tools:
                    # Filter out metadata-related queries that don't need search
                    metadata_terms = ['bench', 'judge', 'court', 'date', 'case number', 'petitioner', 'respondent']
                    needs_search = not any(term in question.lower() for term in metadata_terms)
                    
                    if needs_search:
                        logger.info(f"Trying search tools with query: {search_query}")
                        
                        # Try each search tool until one works
                        for tool in search_tools:
                            try:
                                tool_results = tool.invoke(search_query)
                                if tool_results:
                                    search_results = tool_results
                                    logger.info(f"Got results from {tool.__class__.__name__}")
                                    break
                            except Exception as e:
                                logger.warning(f"Search tool {tool.__class__.__name__} failed: {e}")
                                continue
                
            # If we still have no results, try a simple call to Google
            if not search_results and GOOGLE_API_KEY and GOOGLE_CSE_ID:
                import requests
                try:
                    # Build the Google Custom Search JSON API URL
                    endpoint = "https://www.googleapis.com/customsearch/v1"
                    params = {
                        'key': GOOGLE_API_KEY,
                        'cx': GOOGLE_CSE_ID,
                        'q': search_query
                    }
                    
                    response = requests.get(endpoint, params=params)
                    response.raise_for_status()  # Raise an exception for non-200 responses
                    
                    data = response.json()
                    if 'items' in data:
                        formatted_results = []
                        for item in data['items'][:3]:  # Limit to top 3 results
                            formatted_results.append({
                                "title": item.get('title', 'No title'),
                                "link": item.get('link', '#'),
                                "snippet": item.get('snippet', 'No description available')
                            })
                        search_results = formatted_results
                        logger.info("Got results from direct Google Search API call")
                except Exception as e:
                    logger.warning(f"Direct Google Search API call failed: {e}")
                    
            logger.info(f"Final search results count: {len(search_results) if isinstance(search_results, list) else 'unknown'}")
                
        except Exception as e:
            logger.warning(f"Error in search process: {e}")
            logger.warning(f"Will proceed without search results")
        
        # Create messages for the conversation, including original document if available
        messages = [
            SystemMessage(content="""You are a legal expert assistant specializing in Indian law, including BNS, BNSS, BSA, IPC, CrPC, and PMLA. 
            Use your legal expertise to answer follow-up questions about the judgment analysis.
            Be specific, accurate, and refer to relevant sections of law and precedents.
            Focus on providing factual, helpful information rather than opinions.
            
            VERY IMPORTANT: Pay close attention to all details in the judgment document and input text.
            If the user asks about basic information that is directly stated in the document
            (like names, dates, bench composition, case numbers), ALWAYS check the original text first.
            
            When answering legal questions that require additional context beyond the document:
            1. First check if the answer is in the document text or analysis
            2. Then consider the search results provided (if any)
            3. Provide a comprehensive answer that combines document information with broader legal context
            
            Remember that the judgment may contain key details like:
            - Names of judges in the bench
            - Case number and year
            - Petitioner and respondent information
            - Dates of hearings and judgment
            - Acts and sections cited
            
            IF search results are provided, use them to enhance your answer with real-world legal context.
            DO NOT state that information is missing if it's clearly present in the document text.
            DO NOT state that you cannot search the internet - if search results are provided, use them."""),
        ]
        
        # Include original judgment text if available
        if original_input:
            messages.append(HumanMessage(content=f"Here is the original judgment text:\n\n{original_input}"))
            
        messages.append(HumanMessage(content=f"Here is my analysis of the judgment:\n\n{analysis_content}"))
        
        # Add search results if available
        if search_results:
            search_content = "Search results related to your question:\n\n"
            if isinstance(search_results, list):
                for i, result in enumerate(search_results[:3], 1):  # Limit to top 3 results
                    if isinstance(result, dict):
                        title = result.get('title', f'Result {i}')
                        snippet = result.get('snippet', result.get('content', 'No description available'))
                        url = result.get('link', result.get('url', '#'))
                        search_content += f"{i}. {title}\n{snippet}\nSource: {url}\n\n"
                    else:
                        search_content += f"{i}. {str(result)}\n\n"
            else:
                search_content += str(search_results)
            
            messages.append(HumanMessage(content=f"{search_content}"))
            logger.info("Added search results to conversation")
        else:
            messages.append(HumanMessage(content="Note: No relevant search results were found for this specific query."))
            logger.info("No search results to add")
        
        # Add the user question
        messages.append(HumanMessage(content=f"Based on the above information, please answer this question: {question}"))
        
        # Get response from LLM
        try:
            response = self.llm.invoke(messages)
            return response.content
        except Exception as e:
            error_msg = f"Error processing follow-up question: {str(e)}"
            logger.error(error_msg)
            return f"I encountered an error processing your question: {error_msg}"
            
    def _construct_legal_search_query(self, question: str, original_input: str = None) -> str:
        """
        Construct an optimized search query for legal questions.
        
        Args:
            question: The user's question
            original_input: The original judgment text/content
            
        Returns:
            An optimized search query
        """
        # Start with the basic question
        search_query = question.strip()
        
        # Try to extract key entities to enhance the query
        legal_entities = []
        
        # Check if we have metadata
        if hasattr(self, 'metadata') and self.metadata:
            # Add case number if available and relevant
            if self.metadata.get('case_number') and ('precedent' in question.lower() or 'similar' in question.lower()):
                legal_entities.append(self.metadata.get('case_number'))
                
            # Add court information if available
            if self.metadata.get('court'):
                legal_entities.append(self.metadata.get('court'))
        
        # Look for legal act references in the question and original input
        act_patterns = [
            r'(BNS|BNSS|BSA|IPC|CrPC|PMLA)',
            r'(Bharatiya Nyaya Sanhita|Bharatiya Nagarik Suraksha Sanhita|Bharatiya Sakshya Adhiniyam)',
            r'(Indian Penal Code|Criminal Procedure Code|Evidence Act)',
            r'[Ss]ection\s+(\d+[A-Z]?)'
        ]
        
        # Check question first
        for pattern in act_patterns:
            matches = re.findall(pattern, question)
            for match in matches:
                if isinstance(match, tuple):
                    legal_entities.extend([m for m in match if m])
                else:
                    legal_entities.append(match)
        
        # Also check original input if no matches found in question
        if not legal_entities and original_input:
            for pattern in act_patterns:
                matches = re.findall(pattern, original_input)
                for match in matches:
                    if isinstance(match, tuple):
                        legal_entities.extend([m for m in match if m])
                    else:
                        legal_entities.append(match)
        
        # Extract any years mentioned
        years = re.findall(r'\b(19|20)\d{2}\b', question)
        legal_entities.extend(years)
        
        # Special handling for specific question types
        question_lower = question.lower()
        
        # Precedent questions
        if any(term in question_lower for term in ['precedent', 'landmark', 'similar case', 'prior case']):
            search_query = f"landmark precedent {search_query}"
            
        # Appeals-related
        elif any(term in question_lower for term in ['appeal', 'overturn', 'higher court']):
            search_query = f"appeal process {search_query}"
            
        # Legal procedures
        elif any(term in question_lower for term in ['procedure', 'process', 'timeline']):
            search_query = f"legal procedure {search_query}"
            
        # Acts or sections explanation
        elif re.search(r'section|act|bns|bnss|bsa|ipc|crpc', question_lower):
            # Extract the specific section being referenced
            section_match = re.search(r'section\s+(\d+[A-Z]?)', question_lower)
            act_match = re.search(r'(bns|bnss|bsa|ipc|crpc|pmla)', question_lower)
            
            if section_match and act_match:
                # Specific section in specific act
                section_num = section_match.group(1)
                act_name = act_match.group(1).upper()
                search_query = f"{act_name} Section {section_num} explanation India law"
            elif act_match:
                # Just the act reference
                act_name = act_match.group(1).upper()
                search_query = f"{act_name} India law explanation"
                
        # Build the enhanced query with the legal entities
        unique_entities = list(set(legal_entities))  # Remove duplicates
        if unique_entities:
            entity_str = ' '.join(unique_entities)
            search_query = f"{search_query} {entity_str}"
            
        # Add standard legal search terms for Indian context
        search_query = f"{search_query} Indian judiciary"
        
        return search_query


class JudgementAnalyser:
    """
    Main class for the JudgementAnalyser application.
    Serves as the facade for all judgement analysis capabilities.
    """
    
    def __init__(self):
        """Initialize the JudgementAnalyser application."""
        logger.info("Initializing JudgementAnalyser")
        self.analyser_agent = JudgementAnalyserAgent()

    def get_section_mapping(self, code: str, section: str) -> Dict[str, Any]:
        """
        🔥 PUBLIC API: Get mapping between old and new legal codes.

        Args:
            code: Legal code (BNS, BNSS, BSA, IPC, CrPC, Evidence Act)
            section: Section number

        Returns:
            Dictionary with mapping details
        """
        return self.analyser_agent.get_section_mapping(code, section)

    def format_section_with_mapping(self, code: str, section: str) -> str:
        """
        🔥 PUBLIC API: Format section with proper old/new code mapping.

        Args:
            code: Legal code
            section: Section number

        Returns:
            Formatted string with mapping
        """
        return self.analyser_agent.format_section_with_mapping(code, section)

    def analyze_legal_sections(self, text: str) -> Dict[str, List[Dict]]:
        """
        🔥 PUBLIC API: Extract and analyze all legal sections from text.

        Args:
            text: Text to analyze

        Returns:
            Dictionary with categorized sections and their mappings
        """
        return self.analyser_agent.get_all_related_sections(text)

    def demonstrate_mapping_capabilities(self) -> Dict[str, Any]:
        """
        🔥 DEMO: Demonstrate the comprehensive mapping capabilities.

        Returns:
            Dictionary showing mapping examples and capabilities
        """
        demo_data = {
            "mapping_examples": {
                "ipc_to_bns": [
                    self.get_section_mapping("IPC", "302"),  # Murder
                    self.get_section_mapping("IPC", "420"),  # Cheating
                    self.get_section_mapping("IPC", "375"),  # Rape
                ],
                "crpc_to_bnss": [
                    self.get_section_mapping("CrPC", "154"),  # FIR
                    self.get_section_mapping("CrPC", "41"),   # Arrest
                    self.get_section_mapping("CrPC", "437"),  # Bail
                ],
                "evidence_act_to_bsa": [
                    self.get_section_mapping("Evidence Act", "65A"),  # Electronic evidence
                    self.get_section_mapping("Evidence Act", "45"),   # Expert testimony
                    self.get_section_mapping("Evidence Act", "25"),   # Police confessions
                ]
            },
            "reverse_mapping_examples": {
                "bns_to_ipc": [
                    self.get_section_mapping("BNS", "103"),  # Murder
                    self.get_section_mapping("BNS", "318"),  # Cheating
                    self.get_section_mapping("BNS", "63"),   # Rape
                ],
                "bnss_to_crpc": [
                    self.get_section_mapping("BNSS", "173"), # FIR
                    self.get_section_mapping("BNSS", "35"),  # Arrest
                    self.get_section_mapping("BNSS", "479"), # Bail
                ],
                "bsa_to_evidence_act": [
                    self.get_section_mapping("BSA", "63"),   # Electronic evidence
                    self.get_section_mapping("BSA", "45"),   # Expert testimony
                    self.get_section_mapping("BSA", "25"),   # Police confessions
                ]
            },
            "formatting_examples": [
                self.format_section_with_mapping("IPC", "302"),
                self.format_section_with_mapping("BNS", "103"),
                self.format_section_with_mapping("CrPC", "154"),
                self.format_section_with_mapping("BNSS", "173"),
                self.format_section_with_mapping("Evidence Act", "65A"),
                self.format_section_with_mapping("BSA", "63"),
            ],
            "statistics": {
                "total_bns_mappings": len(self.analyser_agent.bns_section_mapping),
                "total_bnss_mappings": len(self.analyser_agent.bnss_section_mapping),
                "total_bsa_mappings": len(self.analyser_agent.bsa_section_mapping),
            }
        }

        return demo_data

    def analyze_judgement(self, judgement_details: str, crime_details: str = None,
                         evidence_details: str = None, verify: bool = True, 
                         metadata: Dict[str, Any] = None, include_detailed_analysis: bool = False) -> Dict[str, Any]:
        """
        Analyze a legal judgement and provide comprehensive assessment.
        
        Args:
            judgement_details: The judgement issued by the court
            crime_details: Details of the alleged crime
            evidence_details: Details of evidence presented
            verify: Whether to verify legal references with supporting evidence
            metadata: Dictionary with case metadata (case_number, bench, etc.)
            include_detailed_analysis: Whether to include detailed section-by-section analysis
            
        Returns:
            A dictionary with the analysis results
        """
        logger.info(f"Analyzing judgement: {judgement_details[:50]}...")
        query = JudgementQuery(
            judgement_details=judgement_details,
            crime_details=crime_details,
            evidence_details=evidence_details,
            metadata=metadata
        )
        
        result = self.analyser_agent.analyze_judgement(query, metadata=metadata, include_detailed_analysis=include_detailed_analysis)
        return result
    
    def analyze_pdf_judgement(self, pdf_bytes, include_detailed_analysis: bool = False) -> Dict[str, Any]:
        """
        Analyze a judgement from a PDF file.
        
        Args:
            pdf_bytes: Raw bytes of the PDF file
            include_detailed_analysis: Whether to include detailed section-by-section analysis
            
        Returns:
            A dictionary with the analysis results and extracted text sections
        """
        logger.info("Processing PDF judgement...")
        
        # Extract text sections from the PDF
        sections = PDFProcessor.process_pdf_judgement(pdf_bytes)
        
        # Extract metadata from sections
        metadata = {
            "case_number": sections.get("case_number", ""),
            "petitioner": sections.get("petitioner", ""),
            "respondent": sections.get("respondent", ""),
            "date_of_judgement": sections.get("date_of_judgement", ""),
            "bench": sections.get("bench", ""),
            "court": sections.get("court", "")
        }
        
        # Analyze extracted sections
        result = self.analyze_judgement(
            judgement_details=sections["judgement_details"],
            crime_details=sections["crime_details"],
            evidence_details=sections["evidence_details"],
            metadata=metadata,
            include_detailed_analysis=include_detailed_analysis
        )
        
        # Add the extracted sections to the result
        result["extracted_sections"] = sections
        
        return result
    
    def stream_analysis(self, judgement_details: str, crime_details: str = None, 
                       evidence_details: str = None):
        """
        Stream the analysis of a legal judgement as it is generated.
        
        Args:
            judgement_details: The judgement issued by the court
            crime_details: Details of the alleged crime
            evidence_details: Details of evidence presented
            
        Returns:
            A generator yielding chunks of the analysis
        """
        logger.info(f"Streaming analysis for judgement: {judgement_details[:50]}...")
        query = JudgementQuery(
            judgement_details=judgement_details,
            crime_details=crime_details,
            evidence_details=evidence_details
        )
        
        for chunk in self.analyser_agent.stream_analysis(query):
            yield chunk
            
    def stream_pdf_analysis(self, pdf_bytes):
        """
        Stream the analysis of a legal judgement from a PDF file.
        
        Args:
            pdf_bytes: Raw bytes of the PDF file
            
        Returns:
            A generator yielding chunks of the analysis
        """
        logger.info("Processing and streaming PDF judgement analysis...")
        
        # Extract text sections from the PDF
        sections = PDFProcessor.process_pdf_judgement(pdf_bytes)
        
        # Stream analysis from extracted sections
        for chunk in self.stream_analysis(
            judgement_details=sections["judgement_details"],
            crime_details=sections["crime_details"],
            evidence_details=sections["evidence_details"]
        ):
            yield chunk

    def answer_follow_up_question(self, question: str, previous_analysis: str = None, original_input: str = None) -> str:
        """
        Answer a follow-up question about a previously analyzed judgement.
        
        Args:
            question: The follow-up question about the judgement or analysis
            previous_analysis: The previous analysis content (optional)
            original_input: The original judgement text/content (optional)
            
        Returns:
            Answer to the follow-up question
        """
        logger.info(f"Processing follow-up question: {question[:50]}...")
        return self.analyser_agent.answer_follow_up_question(question, previous_analysis, original_input)


# Example usage
if __name__ == "__main__":
    # Initialize JudgementAnalyser
    analyser = JudgementAnalyser()
    
    print("=" * 90)
    print("🏛️  ADVANCED JUDGEMENT ANALYSIS SYSTEM - SUPREME COURT OF INDIA")
    print("=" * 90)
    print("This system provides comprehensive analysis of legal judgements based on:")
    print("• Bharatiya Nyaya Sanhita (BNS) - New Criminal Code")
    print("• Bharatiya Nagarik Suraksha Sanhita (BNSS) - New Criminal Procedure Code")
    print("• Bharatiya Sakshya Adhiniyam (BSA) - New Evidence Act")
    print("• Prevention of Money Laundering Act (PMLA)")
    print("• Supreme Court & High Court Precedents")
    print("=" * 90)
    
    # Ask user for input method
    print("\n📋 How would you like to provide the judgement details?")
    print("1. 📝 Enter judgement details manually")
    print("2. 📄 Upload a judgement PDF file")
    
    choice = input("\n🔸 Enter your choice (1/2): ").strip()
    
    # Variables to track original input
    original_judgement_text = ""
    original_crime_details = ""
    original_evidence_details = ""
    metadata = {}
    
    if choice == "1":
        # Manual entry option
        print("\n" + "─" * 50)
        print("📝 MANUAL ENTRY MODE")
        print("─" * 50)
        
        # Ask for case metadata
        print("\n📊 CASE METADATA (Optional - Press Enter to skip)")
        print("─" * 30)
        case_number = input("📋 Case Number: ").strip()
        if case_number:
            metadata["case_number"] = case_number
            
        bench = input("⚖️  Bench (Judges): ").strip()
        if bench:
            metadata["bench"] = bench
            
        petitioner = input("👤 Petitioner/Appellant: ").strip()
        if petitioner:
            metadata["petitioner"] = petitioner
            
        respondent = input("👤 Respondent/Defendant: ").strip()
        if respondent:
            metadata["respondent"] = respondent
            
        date_of_judgement = input("📅 Date of Judgement: ").strip()
        if date_of_judgement:
            metadata["date_of_judgement"] = date_of_judgement
            
        court = input("🏛️  Court: ").strip()
        if court:
            metadata["court"] = court
        
        print("\n" + "─" * 50)
        print("📋 CASE DETAILS")
        print("─" * 50)
        
        # Get judgement details
        print("⚖️  Judgement Details (Court's Decision):")
        original_judgement_text = input("Enter details: ").strip() or "Accused under PMLA found guilty of money laundering and sentenced to 5 years imprisonment with fine of Rs. 10 lakhs"
        
        print("\n🔍 Crime Details (Optional - Press Enter to skip):")
        original_crime_details = input("Enter details: ").strip() or "Opposition party filed a case against the accused for money laundering and corruption involving Rs. 50 crores"
        
        print("\n📄 Evidence Details (Optional - Press Enter to skip):")
        original_evidence_details = input("Enter details: ").strip() or "An accused who was under ED custody confessed to the crime and provided evidence against the person, after which ED released them"
        
        # Ask for analysis type
        print("\n" + "─" * 50)
        print("🎯 ANALYSIS TYPE")
        print("─" * 50)
        print("1. 📊 Comprehensive Overview (Recommended)")
        print("2. 📋 Comprehensive + Detailed Section-by-Section Analysis")
        
        analysis_choice = input("\n🔸 Choose analysis type (1/2): ").strip()
        include_detailed = (analysis_choice == "2")
        
        print(f"\n🔄 Analyzing judgement{'with detailed analysis' if include_detailed else ''}...")
        result = analyser.analyze_judgement(
            original_judgement_text, 
            original_crime_details, 
            original_evidence_details, 
            metadata=metadata,
            include_detailed_analysis=include_detailed
        )
        
    elif choice == "2":
        # PDF upload option
        print("\n" + "─" * 50)
        print("📄 PDF UPLOAD MODE")
        print("─" * 50)
        pdf_path = input("📁 Enter the path to the judgement PDF file: ").strip()
        
        try:
            # Read the PDF file as bytes
            with open(pdf_path, 'rb') as file:
                pdf_bytes = file.read()
            
            print("\n🔄 Processing PDF...")
            # Extract text before analysis to keep track of original content
            sections = PDFProcessor.process_pdf_judgement(pdf_bytes)
            original_judgement_text = sections["judgement_details"]
            original_crime_details = sections["crime_details"] 
            original_evidence_details = sections["evidence_details"]
            
            # Extract metadata
            metadata = {
                "case_number": sections.get("case_number", ""),
                "petitioner": sections.get("petitioner", ""),
                "respondent": sections.get("respondent", ""),
                "date_of_judgement": sections.get("date_of_judgement", ""),
                "bench": sections.get("bench", ""),
                "court": sections.get("court", "")
            }
            
            # Display extracted metadata
            print("\n" + "─" * 50)
            print("📊 EXTRACTED METADATA")
            print("─" * 50)
            print(f"📋 Case Number: {metadata.get('case_number', 'Not found')}")
            print(f"👤 Petitioner: {metadata.get('petitioner', 'Not found')}")
            print(f"👤 Respondent: {metadata.get('respondent', 'Not found')}")
            print(f"📅 Date of Judgement: {metadata.get('date_of_judgement', 'Not found')}")
            print(f"⚖️  Bench: {metadata.get('bench', 'Not found')}")
            print(f"🏛️  Court: {metadata.get('court', 'Not found')}")
            
            correct_metadata = input("\n🔸 Do you want to correct any metadata? (yes/no): ").strip().lower()
            if correct_metadata == 'yes':
                new_bench = input(f"⚖️  Bench [{metadata.get('bench', '')}]: ").strip()
                if new_bench:
                    metadata["bench"] = new_bench
                    
                new_case_number = input(f"📋 Case Number [{metadata.get('case_number', '')}]: ").strip()
                if new_case_number:
                    metadata["case_number"] = new_case_number
                    
                new_date = input(f"📅 Date of Judgement [{metadata.get('date_of_judgement', '')}]: ").strip()
                if new_date:
                    metadata["date_of_judgement"] = new_date
            
            # Ask for analysis type
            print("\n" + "─" * 50)
            print("🎯 ANALYSIS TYPE")
            print("─" * 50)
            print("1. 📊 Comprehensive Overview (Recommended)")
            print("2. 📋 Comprehensive + Detailed Section-by-Section Analysis")
            
            analysis_choice = input("\n🔸 Choose analysis type (1/2): ").strip()
            include_detailed = (analysis_choice == "2")
            
            # Analyze the extracted text
            print(f"\n🔄 Analyzing judgement{'with detailed analysis' if include_detailed else ''}...")
            result = analyser.analyze_judgement(
                original_judgement_text,
                original_crime_details,
                original_evidence_details,
                metadata=metadata,
                include_detailed_analysis=include_detailed
            )
            
        except FileNotFoundError:
            print(f"\n❌ Error: File not found - {pdf_path}")
            print("Please check the file path and try again.")
            exit(1)
        except Exception as e:
            print(f"\n❌ Error processing PDF: {e}")
            exit(1)
            
    else:
        print("\n❌ Invalid choice. Please run the program again and select option 1 or 2.")
        exit(1)
    
    # Display results
    print("\n" + "=" * 90)
    print("📊 COMPREHENSIVE JUDGEMENT ANALYSIS RESULTS")
    print("=" * 90)
    
    if "messages" in result and result["messages"]:
        analysis_content = result["messages"][-1].content
        print(analysis_content)
    else:
        analysis_content = str(result)
        print(analysis_content)
        
    print("\n" + "=" * 90) 
    
    # Prepare the full original input text for Q&A, including metadata if available
    original_input = ""
    
    # Add metadata if available
    if metadata:
        original_input += f"""
🏛️  CASE INFORMATION:
📋 Case Number: {metadata.get('case_number', 'Not specified')}
👤 Petitioner/Appellant: {metadata.get('petitioner', 'Not specified')}
👤 Respondent/Defendant: {metadata.get('respondent', 'Not specified')}
📅 Date of Judgement: {metadata.get('date_of_judgement', 'Not specified')}
⚖️  Bench: {metadata.get('bench', 'Not specified')}
🏛️  Court: {metadata.get('court', 'Not specified')}

"""
    
    # Add the judgment text
    original_input += f"""
⚖️  JUDGEMENT DETAILS:
{original_judgement_text}

🔍 CRIME DETAILS:
{original_crime_details}

📄 EVIDENCE DETAILS:
{original_evidence_details}
"""
    
    # Interactive Q&A mode
    print("\n🤔 Would you like to ask questions about this analysis?")
    qa_choice = input("🔸 Enter 'yes' to continue or any other key to exit: ").strip().lower()
    
    if qa_choice == 'yes':
        print("\n" + "=" * 90)
        print("🤖 ENHANCED INTERACTIVE Q&A MODE WITH GOOGLE SEARCH")
        print("=" * 90)
        print("Ask any questions about the judgement or the analysis.")
        print("The system will use Google Search API to find relevant information.")
        print("\n💡 You can ask about:")
        print("• 📋 Case details (bench, parties, dates, etc.)")
        print("• ⚖️  Legal interpretations and precedents")
        print("• 🔄 Alternative judgements or appeals")
        print("• 📚 Specific laws or sections mentioned")
        print("• 🔍 Related cases or broader legal context")
        print("• 📊 Pros and cons analysis")
        print("• 🎯 Strategic advice for parties")
        print("\n💬 Type 'exit' or 'quit' to end the session.")
        
        # Test Google Search API
        print("\n🔍 Verifying Google Search API connectivity...")
        try:
            google_search = GoogleSearchAPIWrapper(
                google_api_key=GOOGLE_API_KEY,
                google_cse_id=GOOGLE_CSE_ID
            )
            test_result = google_search.run("test query")
            if test_result:
                print("✅ Google Search API is connected and working.")
            else:
                print("⚠️  Google Search API returned empty results. May not work properly.")
        except Exception as e:
            print(f"❌ Google Search API could not be initialized: {str(e)}")
            print("Will attempt alternative search methods.")
        
        # Keep track of question types
        question_count = 0
        legal_questions = 0
        
        while True:
            # Show custom prompt based on question history
            if question_count > 3 and legal_questions > 0:
                print("\n💡 You can ask deeper legal questions about relevant precedents, sections, or comparative analysis.")
                
            user_question = input("\n🤔 Your question: ").strip()
            
            if user_question.lower() in ['exit', 'quit', 'bye', 'goodbye']:
                print("\n👋 Thank you for using the Advanced Judgement Analysis System. Goodbye!")
                break
                
            if not user_question:
                print("❓ Please ask a valid question.")
                continue
            
            # Categorize the question
            metadata_terms = ['bench', 'judge', 'court', 'date', 'case number', 'petitioner', 'respondent', 
                             'filed', 'decided', 'heard']
            legal_terms = ['section', 'act', 'law', 'precedent', 'appeal', 'legal', 'procedure', 'code', 
                          'bns', 'bnss', 'bsa', 'ipc', 'crpc', 'evidence', 'validity', 'right', 'constitution']
            
            is_metadata_question = any(term in user_question.lower() for term in metadata_terms)
            is_legal_question = any(term in user_question.lower() for term in legal_terms)
            
            question_count += 1
            if is_legal_question:
                legal_questions += 1
                print("\n🔍 Processing your legal question...")
                if not is_metadata_question:
                    print("🔍 Using Google Search API to find relevant legal information...")
            else:
                print("\n🔄 Processing your question...")
            
            try:
                # Create progress indicator
                import threading
                import time
                
                stop_flag = threading.Event()
                
                def progress_indicator():
                    indicators = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏']
                    i = 0
                    while not stop_flag.is_set():
                        print(f"\r🔍 Searching {indicators[i]} ", end='', flush=True)
                        i = (i + 1) % len(indicators)
                        time.sleep(0.1)
                    print("\r🔍 Search completed! ✅          ", flush=True)
                
                # Start progress indicator for search queries
                if not is_metadata_question and (is_legal_question or "search" in user_question.lower()):
                    progress_thread = threading.Thread(target=progress_indicator)
                    progress_thread.daemon = True
                    progress_thread.start()
                
                # Use the follow-up question method
                response = analyser.answer_follow_up_question(
                    user_question, 
                    analysis_content, 
                    original_input
                )
                
                # Stop progress indicator
                stop_flag.set()
                if 'progress_thread' in locals() and progress_thread.is_alive():
                    progress_thread.join(0.5)
                
                # Display response
                print("\n" + "─" * 90)
                print("📋 RESPONSE:")
                print("─" * 90)
                print(response)
                print("─" * 90)
                
                # Give hints
                if question_count == 3:
                    print("\n💡 TIP: You can ask about specific sections of law, precedents, or request comparative analysis.")
                
            except Exception as e:
                # Stop progress indicator
                if 'stop_flag' in locals():
                    stop_flag.set()
                if 'progress_thread' in locals() and progress_thread.is_alive():
                    progress_thread.join(0.5)
                    
                print(f"\n❌ Error processing question: {str(e)}")
                print("Please try a different question.")